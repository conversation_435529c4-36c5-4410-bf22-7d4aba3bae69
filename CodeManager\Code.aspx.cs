﻿using Account.Web.Common;
using CommonLib;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text.RegularExpressions;
using System.Web;

namespace Account.Web
{
    public partial class Code : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            string strOp = BoxUtil.GetStringFromObject(Request.QueryString["op"]);
            if (!string.IsNullOrEmpty(strOp))
            {
                DoOperate(strOp);
            }
            Response.End();
        }

        private static List<string> lstMsg = new List<string>();
        private static object objLock = "";

        private void ResponseWriteByIsWeb(string strMsg, bool isWeb = false, string location = "")
        {
            Response.Write(string.Format("{0}{1}{2}"
                , isWeb ? "<script>alert('" : ""
                , strMsg
                , isWeb ? "');" + (string.IsNullOrEmpty(location) ? "" : "window.location='" + location + "'") + "</script>" : ""));
        }

        private void DoOperate(string strOP)
        {
            var code = new CodeEntity
            {
                StrAppCode = Request.GetValue("app")
            };
            var lang = Request.GetValue("lang");
            if (strOP == "pay")
            {
                //http://ocr.oldfish.cn:9090/testOrder?isHtml=1&type=0&price=0.01&return_url=http%3A%2F%2Focr.oldfish.cn%3A2020%2FPay.aspx&notify_url=http%3A%2F%2Focr.oldfish.cn%3A2020%2FPay.ashx&remark=OCR%E5%8A%A9%E6%89%8B-%E4%B8%80%E5%B9%B4%E4%B8%93%E4%B8%9A%E7%89%88&param=***********
                var account = BoxUtil.GetStringFromObject(Request.QueryString["account"]);
                var orderNo = BoxUtil.GetStringFromObject(Request.QueryString["orderNo"]);
                var remark = HttpUtility.UrlDecode(BoxUtil.GetStringFromObject(Request.QueryString["remark"]));
                var from = (OrderFrom)BoxUtil.GetInt32FromObject(Request.QueryString["from"], 0);
                string price = "";

                if (!string.IsNullOrEmpty(remark))
                {
                    var userType = GetCanRegUserTypes().FirstOrDefault(p => remark.EndsWith(p.Type.ToString()));
                    if (userType != null && userType.UserChargeType.Count > 0)
                    {
                        var priceType = userType.UserChargeType.FirstOrDefault(p => remark.StartsWith(p.Name)) ??
                                        userType.UserChargeType.FirstOrDefault();
                        if (priceType != null)
                        {
                            price = priceType.Price.ToString("F0");
                            remark = string.Format("{0}{1}", priceType.Name, userType.Type);
                        }
                    }
                    if (string.IsNullOrEmpty(price))
                    {
                        userType = GetCanRegUserTypes(true).FirstOrDefault(p => remark.EndsWith(p.Type.ToString()));
                        if (userType != null && userType.UserChargeType.Count > 0)
                        {
                            var priceType = userType.UserChargeType.FirstOrDefault(p => remark.StartsWith(p.Name)) ??
                                            userType.UserChargeType.FirstOrDefault();
                            if (priceType != null)
                            {
                                price = priceType.Price.ToString("F0");
                                remark = string.Format("{0}{1}", priceType.Name, userType.Type);
                            }
                        }
                    }
                }
                LogHelper.Log.Error("发起支付入参：" + string.Format("price:{0}, orderNo:{1}, remark:{2}, account:{3}", price, orderNo, Request.QueryString["remark"], account));

                if (!BoxUtil.IsEmail(account) && !BoxUtil.IsMobile(account))
                {
                    Response.Write(UserConst.StrAccountFormatError.GetTrans(lang));
                }
                else
                {
                    var result = PayUtil.GetPayUrl(price, orderNo, remark, account, from);
                    Response.Write(result);
                }
            }
            else if (strOP == "pay_BuDan")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.BuDan(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "pay_Del")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.DelOrder(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "getOrder")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.GetPayOrderInfo(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "checkOrder")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var result = PayUtil.CheckPayOrderState(orderId);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "changePayType")
            {
                var orderId = BoxUtil.GetStringFromObject(Request.Form["orderId"]);
                var payType = BoxUtil.GetInt32FromObject(Request.Form["payType"]);
                var result = PayUtil.ChangePayType(orderId, payType);
                Response.ContentType = "application/json";
                Response.Write(result);
            }
            else if (strOP == "msg")
            {
                var msg = "";
                lock (objLock)
                {
                    if (lstMsg.Count > 0)
                    {
                        msg = lstMsg[0];
                        lstMsg.Remove(msg);
                    }
                }
                if (!string.IsNullOrEmpty(msg))
                {
                    Response.Write(msg);
                }
                else
                {
                    Response.Write("no");
                }
            }
            else if (strOP == "reg")
            {
                code.StrAppCode = Request.GetValue("account");
                if (string.IsNullOrEmpty(code.StrAppCode))
                    code.StrAppCode = Request.GetValue("app");
                if (CodeHelper.IsExitsCode(code.StrAppCode))
                {
                    Response.Write(UserConst.StrHasReged.GetTrans(lang));
                }
                else
                {
                    var isEmail = BoxUtil.IsEmail(code.StrAppCode);
                    var isMobile = BoxUtil.IsMobile(code.StrAppCode);
                    if (isEmail || isMobile)
                    {
                        code.StrPwd = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                        if (string.IsNullOrEmpty(code.StrPwd))
                        {
                            Response.Write(UserConst.StrAccountEmpty.GetTrans(lang));
                        }
                        else
                        {
                            var validateCode = BoxUtil.GetStringFromObject(Request.QueryString["code"]);
                            if (string.IsNullOrEmpty(validateCode))
                            {
                                Response.Write(UserConst.StrValidateCodeEmpty.GetTrans(lang));
                            }
                            else
                            {
                                //使用regex进行格式设置 至少有数字、大小写字母，最少8个字符、最长30个字符
                                if (!Regex.IsMatch(code.StrPwd, @"[0-9A-Za-z].{5,15}"))//判断密码格式是否符合要求
                                {
                                    Response.Write(UserConst.StrPwdFormatError.GetTrans(lang));
                                }
                                else
                                {
                                    code.StrNickName = BoxUtil.GetStringFromObject(Request.QueryString["nick"]);
                                    if (code.StrNickName.Length < 2 || code.StrNickName.Length > 20 || !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(code.StrNickName))
                                    {
                                        Response.Write(UserConst.StrNickNameFormatError.GetTrans(lang));
                                    }
                                    else
                                    {
                                        code.DtReg = ServerTime.LocalTime;
                                        if (isEmail && DisposeEmailHelper.IsDisposeEmail(code.StrAppCode))
                                        {
                                            LogHelper.Log.Info(string.Format("临时邮箱:{0}", JsonConvert.SerializeObject(code)));
                                            code.StrType = UserTypeEnum.体验版.ToString();
                                            code.DtExpire = code.DtReg;
                                        }
                                        else
                                        {
                                            if (ConfigHelper.IsRegToGeRen)
                                            {
                                                code.StrType = UserTypeEnum.个人版.ToString();
                                                code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                                            }
                                            else if (ConfigHelper.IsRegToProfessional)
                                            {
                                                code.StrType = UserTypeEnum.专业版.ToString();
                                                code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                                            }
                                            else
                                            {
                                                code.StrType = UserTypeEnum.体验版.ToString();
                                                code.DtExpire = code.DtReg;
                                            }
                                        }

                                        if (string.IsNullOrEmpty(code.StrNickName))
                                        {
                                            code.StrNickName = code.StrAppCode;
                                        }
                                        var validateMd5 = CommonValidateCode.GetValidateCode(code.StrAppCode, "OCRREG");
                                        if (validateMd5.Equals(validateCode.ToUpper()))
                                        {
                                            code.StrPwd = CommonValidateCode.GetMD5String(code.StrPwd + "OCRREG").ToUpper();
                                            Response.Write("True");
                                            Add(code);
                                        }
                                        else
                                        {
                                            Response.Write(UserConst.StrValidateCodeError.GetTrans(lang));
                                        }
                                    }
                                }

                            }
                        }
                    }
                    else
                    {
                        Response.Write(UserConst.StrAccountFormatError.GetTrans(lang));
                    }
                }
            }
            else if (strOP == "heart")
            {
                //if (!Request.UserAgent.StartsWith("OcrAgent"))
                //{
                //    Request.Log("疑似爬虫！");
                //    Response.Write("True");
                //    return;
                //}
                var uid = Request.GetValue("uid");
                var token = Request.GetValue("token");
                if (string.IsNullOrEmpty(token))
                {
                    if (string.IsNullOrEmpty(uid))
                    {
                        Request.Log("UID为空，验证失败！");
                        Response.Write("True");
                        return;
                    }
                    token = string.IsNullOrEmpty(token) ? uid : token;
                }
                var account = Request.GetValue("app");
                if (string.IsNullOrEmpty(account))
                {
                    account = Request.GetValue("account");
                }
                account = account.ToLower();
                var success = true;
                //检查设备是否被禁用
                if (!string.IsNullOrEmpty(account) && !string.IsNullOrEmpty(token))
                {
                    if (CodeHelper.CheckIsForbid(account, token))
                    {
                        success = false;
                    }
                }
                if (success)
                {
                    var isTest = string.IsNullOrEmpty(account);
                    var testAccount = RdsCacheHelper.LstAccountCache.GetTestUserName(token);
                    account = isTest ? testAccount : account;

                    success = RdsCacheHelper.LstAccountCache.HeartBeat(account, token);
                    if (!isTest)
                    {
                        RdsCacheHelper.LstAccountCache.Remove(testAccount);
                    }
                    try
                    {
                        if (!string.IsNullOrEmpty(uid))
                        {
                            var version = Request.GetValue("version");
                            if (string.IsNullOrEmpty(version))
                            {
                                version = Request.GetValue("ver");
                            }
                            UserData data = new UserData()
                            {
                                Account = account,
                                Token = uid,
                                DtLast = ServerTime.DateTime,
                                Ip = HttpContext.Current.Items["nowIP"]?.ToString(),
                                Mac = Request.GetValue("mac"),
                                lang = Request.GetValue("lang"),
                                version = version,
                            };
                            if (!string.IsNullOrEmpty(data.Mac))
                            {
                                data.Mac = HttpUtility.UrlDecode(data.Mac);
                            }
                            if (string.IsNullOrEmpty(data.Mac))
                            {
                                var old = CodeHelper.GetUserMac(uid);
                                if (old != null && old.Rows.Count > 0)
                                {
                                    if (!string.IsNullOrEmpty(old.Rows[0]["Ip"]?.ToString()))
                                        data.Ip = old.Rows[0]["Ip"]?.ToString();
                                    data.Mac = old.Rows[0]["Mac"]?.ToString();
                                    if (string.IsNullOrEmpty(data.version) && !string.IsNullOrEmpty(old.Rows[0]["version"]?.ToString()))
                                        data.version = old.Rows[0]["version"]?.ToString();
                                    if (string.IsNullOrEmpty(data.lang) && !string.IsNullOrEmpty(old.Rows[0]["lang"]?.ToString()))
                                        data.lang = old.Rows[0]["lang"]?.ToString();
                                }
                                if (string.IsNullOrEmpty(data.Mac))
                                    Request.Log("Mac为空！");
                            }
                            CodeHelper.AddUserData(data);
                        }
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error("Heart失败", oe);
                    }
                }
                Response.Write(success.ToString());
            }
            else if (strOP == "frpc")
            {
                try
                {
                    var strData = Request.Form["frpc"];
                    if (!string.IsNullOrEmpty(strData))
                    {
                        var data = JsonConvert.DeserializeObject<List<FrpcEntity>>(strData);
                        CodeHelper.AddOrUpdateFrpc(data);
                    }
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error("Frpc保存失败", oe);
                }
                try
                {
                    var dtTmp = new DataTable();
                    var mainSite = CodeHelper.GetFastSite(ref dtTmp);
                    var strTmp = JsonConvert.SerializeObject(mainSite).Replace(",\"IsDefault\":false", "").Replace(",\"Host\":null", "");
                    QiNiuUpload.UploadFile(strTmp, "newSite.json");
                }
                catch (Exception oe)
                {
                    LogHelper.Log.Error("生成Site失败:" + oe.Message);
                }
                Response.Write("True");
            }
            else if (strOP == "count")
            {
                var account = Request.GetValue("app");
                if (string.IsNullOrEmpty(account))
                {
                    account = Request.GetValue("account");
                }
                if (string.IsNullOrEmpty(account))
                {
                    var identity = Request.GetValue("uid");
                    if (!string.IsNullOrEmpty(identity))
                    {
                        account = RdsCacheHelper.LstAccountCache.GetTestUserName(identity);
                    }
                }

                var codeCount = new UserCodeCount { Account = account };
                if (!string.IsNullOrEmpty(account))
                {
                    codeCount.TodayCount = RdsCacheHelper.CodeRecordCache.GetUserCodeInfo(account).TodayCount;

                    var userLoginInfo = RdsCacheHelper.LstAccountCache.GetUserInfo(account);
                    if (userLoginInfo != null)
                    {
                        var userType = UserTypeHelper.GetUserType(userLoginInfo.UserType);
                        codeCount.LimitCount = userType.LimitPerDayCount;
                    }
                }
                Response.Write(JsonConvert.SerializeObject(codeCount));
            }
            else if (strOP == "regtype")
            {
                var lstUserType = GetCanRegUserTypes();
                Response.Write(JsonConvert.SerializeObject(lstUserType));
            }
            else if (strOP == "login")
            {
                var isWeb = !string.IsNullOrEmpty(Request.QueryString["isweb"]);
                if (!isWeb && !CommonHelper.IsValidateVersion(Request))
                {
                    ResponseWriteByIsWeb(ConfigHelper.MinVersionStr);
                    return;
                }
                var identity = isWeb ? Guid.NewGuid().ToString().Replace("-", "") : Request.GetValue("uid");
                code.StrAppCode = Request.GetValue("account");
                code.StrPwd = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);

                var strWebLoginUrl = "UserLogin.aspx?username=" + code.StrAppCode + "&password=" + code.StrPwd;

                var isEmail = BoxUtil.IsEmail(code.StrAppCode);
                var isMobile = BoxUtil.IsMobile(code.StrAppCode);
                if (isEmail || isMobile)
                {
                    //检查设备是否被禁用
                    if (CodeHelper.CheckIsForbid(code.StrAppCode, identity))
                    {
                        ResponseWriteByIsWeb("当前设备已经被禁用！", isWeb, strWebLoginUrl);
                        return;
                    }
                    var user = CodeHelper.GetCodeByAccountId(code.StrAppCode);
                    if (user == null || string.IsNullOrEmpty(user.StrAppCode))
                    {
                        ResponseWriteByIsWeb(UserConst.StrAccountNotExsits.GetTrans(lang), isWeb, strWebLoginUrl);
                        return;
                    }
                    var pwd = CommonValidateCode.GetMD5String(code.StrPwd + "OCRREG").ToUpper();
                    if (Equals(pwd, user.StrPwd))
                    {
                        // 1、非体验版
                        // 2、不是通过购买获得的
                        // 3、本机登录设备数大于3
                        if (!Equals(user.StrType, UserTypeEnum.体验版.ToString())
                             && string.IsNullOrEmpty(user.StrRemark)
                             && !CodeHelper.IsCanReg(identity, ""))
                        {
                            LogHelper.Log.Error("封号：" + user.StrAppCode);
                            user.DtExpire = user.DtReg;
                        }
                        var loginInfo = new UserLoginInfo
                        {
                            Account = user.StrAppCode,
                            NickName = user.StrNickName,
                            UserType = (UserTypeEnum)Enum.Parse(typeof(UserTypeEnum), user.StrType),
                            Remark = user.StrRemark,
                            DtExpired = user.DtExpire,
                            DtReg = user.DtReg,
                            DtLogin = ServerTime.LocalTime,
                            LstToken = RdsCacheHelper.LstAccountCache.GetUserInfo(user.StrAppCode)?.LstToken
                        };
                        if (loginInfo.LstToken == null)
                        {
                            loginInfo.LstToken = new List<TokenEntity>();
                        }
                        if (user.IsExpired)
                        {
                            //其他版本(非个人版)到期，自动降级为个人版
                            if (!string.IsNullOrEmpty(user.StrRemark) && user.StrRemark.Contains("版") &&
                                !user.StrRemark.Contains(UserTypeEnum.个人版.ToString()))
                            {
                                code.DtExpire = ServerTime.DateTime.AddYears(100);
                                code.StrType = UserTypeEnum.个人版.ToString();

                                Add(code);

                                loginInfo.DtExpired = code.DtExpire;
                                loginInfo.UserType = UserTypeEnum.个人版;
                            }
                            else
                            {
                                //否则，降级为体验版
                                loginInfo.UserType = UserTypeEnum.体验版;
                                if (!Equals(code.StrType, loginInfo.UserType.ToString()))
                                {
                                    code.StrType = loginInfo.UserType.ToString();
                                    Add(code);
                                }
                            }
                        }

                        loginInfo.LstToken.RemoveAll(p => Equals(p.Token, identity));

                        var userTypeInfo = UserTypeHelper.GetUserInfo(loginInfo.UserType);
                        while (loginInfo.LstToken.Count >= userTypeInfo.MaxLoginCount)
                        {
                            loginInfo.LstToken = loginInfo.LstToken.OrderBy(p => p.DtExpired).ToList();
                            loginInfo.LstToken.RemoveAt(0);
                        }

                        loginInfo.UserTypeName = loginInfo.UserType.ToString();
                        loginInfo.IsSetOtherResult = userTypeInfo.IsSetOtherResult;
                        loginInfo.IsSupportBatch = userTypeInfo.IsSupportBatch;
                        loginInfo.IsSupportMath = userTypeInfo.IsSupportMath;
                        loginInfo.IsSupportImageFile = userTypeInfo.IsSupportImageFile;
                        loginInfo.IsSupportDocFile = userTypeInfo.IsSupportDocFile;
                        loginInfo.IsSupportTable = userTypeInfo.IsSupportTable;
                        loginInfo.IsSupportTxt = userTypeInfo.IsSupportTxt;
                        loginInfo.IsSupportVertical = userTypeInfo.IsSupportVertical;
                        loginInfo.IsSupportTranslate = userTypeInfo.IsSupportTranslate;
                        loginInfo.MaxUploadSize = userTypeInfo.MaxUploadSize;
                        loginInfo.PerTimeSpanExecCount = userTypeInfo.PerTimeSpanExecCount;
                        loginInfo.PerTimeSpan = userTypeInfo.PerTimeSpan;
                        loginInfo.LimitPerDayCount = userTypeInfo.LimitPerDayCount;
                        loginInfo.IsSupportPassage = userTypeInfo.IsSupportPassage;
                        loginInfo.MaxPassageCount = userTypeInfo.MaxPassageCount;
                        loginInfo.IsSupportLocalOcr = userTypeInfo.IsSupportLocalOcr;

                        var token = new TokenEntity
                        {
                            Token = identity,
                            DtExpired = ServerTime.LocalTime.AddHours(1)
                        };
                        loginInfo.Token = token.Token;
                        loginInfo.LstToken.Add(token);

                        RdsCacheHelper.LstAccountCache.InsertOrUpdateUser(loginInfo);

                        CodeHelper.UpdateLastLogin(new List<string> { loginInfo.Account });

                        //不需要返回给用户
                        loginInfo.LstToken = null;
                        if (isWeb)
                        {
                            Response.Cookies.Add(new HttpCookie("token", loginInfo.Token) { Expires = ServerTime.DateTime.AddHours(3) });
                            Response.Cookies.Add(new HttpCookie("account", loginInfo.Account) { Expires = ServerTime.DateTime.AddHours(3) });
                            Response.Redirect("UserIndex.aspx");
                        }
                        else
                        {
                            Response.Write("True|" + JsonConvert.SerializeObject(loginInfo));
                        }
                    }
                    else
                    {
                        ResponseWriteByIsWeb("用户名或密码错误！", isWeb, strWebLoginUrl);
                    }
                }
                else
                {
                    ResponseWriteByIsWeb(UserConst.StrAccountFormatError.GetTrans(lang), isWeb, strWebLoginUrl);
                }
            }
            else if (strOP == "resetpwd")
            {
                code.StrAppCode = Request.GetValue("account");
                if (string.IsNullOrEmpty(code.StrAppCode))
                    code.StrAppCode = Request.GetValue("app");
                if (!CodeHelper.IsExitsCode(code.StrAppCode))
                {
                    Response.Write(UserConst.StrAccountNotExsits.GetTrans(lang));
                }
                else
                {
                    var isEmail = BoxUtil.IsEmail(code.StrAppCode);
                    var isMobile = BoxUtil.IsMobile(code.StrAppCode);
                    if (isEmail || isMobile)
                    {
                        code.StrPwd = BoxUtil.GetStringFromObject(Request.QueryString["pwd"]);
                        if (string.IsNullOrEmpty(code.StrPwd))
                        {
                            Response.Write(UserConst.StrNewPwdEmpty.GetTrans(lang));
                        }
                        else
                        {
                            var validateCode = BoxUtil.GetStringFromObject(Request.QueryString["code"]);
                            var validateMd5 = CommonValidateCode.GetValidateCode(code.StrAppCode, "OCRREGForgetPwd");
                            if (validateMd5.Equals(validateCode?.ToUpper()))
                            {
                                code.StrPwd = CommonValidateCode.GetMD5String(code.StrPwd + "OCRREG").ToUpper();
                                if (CodeHelper.UpdateCodePwd(code.StrAppCode, code.StrPwd))
                                {
                                    Response.Write("True");
                                }
                                else
                                {
                                    Response.Write(UserConst.StrServerError.GetTrans(lang));
                                }
                            }
                            else
                            {
                                Response.Write(UserConst.StrValidateCodeEmpty.GetTrans(lang));
                            }
                        }
                    }
                    else
                    {
                        Response.Write(UserConst.StrAccountFormatError.GetTrans(lang));
                    }
                }
            }
            else if (strOP == "resetnickname")
            {
                code.StrAppCode = Request.GetValue("account");
                if (string.IsNullOrEmpty(code.StrAppCode))
                    code.StrAppCode = Request.GetValue("app");
                if (!CodeHelper.IsExitsCode(code.StrAppCode))
                {
                    Response.Write(UserConst.StrAccountNotExsits.GetTrans(lang));
                }
                else
                {
                    var nickName = Request.QueryString["nick"];
                    if (string.IsNullOrEmpty(nickName) || nickName.Length < 2 || nickName.Length > 20 || !new System.Text.RegularExpressions.Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                    {
                        Response.Write(UserConst.StrNickNameFormatError.GetTrans(lang));
                    }
                    else
                    {
                        if (CodeHelper.UpdateCodeNickName(code.StrAppCode, nickName))
                        {
                            Response.Write("True");
                        }
                        else
                        {
                            Response.Write(UserConst.StrServerError.GetTrans(lang));
                        }
                    }
                }
            }
            else
            {
                switch (strOP)
                {
                    case "del":
                        if (CodeHelper.DelByAppCode(code))
                        {
                            Response.Write("删除成功！");
                        }
                        else
                        {
                            Response.Write("删除失败！");
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        internal class UserCodeCount
        {
            public string Account { get; set; }

            public long TodayCount { get; set; }

            public long LimitCount { get; set; }
        }

        public static List<UserType> GetCanRegUserTypes(bool isApi = false)
        {
            var lstUserType = isApi ? UserTypeHelper.GetCanRegApiTypes() : UserTypeHelper.GetCanRegUserTypes();
            lstUserType.ForEach(p =>
            {
                var lstChargeType = new List<ChargeViewToUser>();
                p.ChargeTypes.ForEach(q =>
                {
                    string strDesc = "";
                    var price = q.GetPrice(p.PerPrice, ref strDesc);
                    var charge = new ChargeViewToUser()
                    {
                        Name = q.Name,
                        Desc = strDesc,
                        Price = (double)price,
                        OriPrice = (double)q.OriPrice,
                        IsDefault = q.IsDefault,
                        Tag = q.Tag,
                    };
                    lstChargeType.Add(charge);
                });
                p.UserChargeType = lstChargeType;
            });
            return lstUserType;
        }

        private void Add(CodeEntity code)
        {
            if (CodeHelper.AddOrUpdateCode(code))
            {
                RdsCacheHelper.LstAccountCache.Remove(code.StrAppCode);
            }
        }
    }
}