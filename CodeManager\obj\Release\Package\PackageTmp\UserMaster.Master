﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="UserMaster.Master.cs" Inherits="Account.Web.UserMaster" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head runat="server">
    <meta charset="UTF-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta name="renderer" content="webkit"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0,maximum-scale=1.0,user-scalable=0"/>
    <link rel="Shortcut Icon" href="/favicon.ico" type="image/x-icon"/>
    <link rel="stylesheet" href="./CSS/bootstrap.min.css">
    <link rel="stylesheet" href="./static/css/main.css">
    <meta name="keywords" content="文字识别,图片识别,OCR识别平台,OCR识别软件,图片文字识别,图片转文字,图片转表格,图片转公式,文字识别软件,在线文字识别,在线OCR,OCR识别,OCR,名片识别,证件识别,文档识别,OCR助手,OCR文字识别助手,OCR图片文字识别助手" />
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <div class="container container-user container-user-center" style="margin-top:0px">
        <div>
            <div class="section section-subnav">
                <div class="section_subnav">
                    <p class="subnav_title"><a href="User.aspx">个人中心</a></p>
                </div>
            </div>

            <div class="section section-user">
                <div class="section_user">
                    <div class="user_sidebar">
                        <div class="sidebar_header">
                            <!-- <p>个人中心</p> -->
                        </div>
                        <ul class="sidebar_nav">
                            <li class="nav_item"><a href="UserIndex.aspx" fr-fix-f13625cc="bold">我的账号</a></li>
                            <li class="nav_item"><a href="UserMac.aspx">我的设备</a></li>
                            <li class="nav_item"><a href="UserIndex.aspx?op=logout">退出登录</a></li>
                        </ul>
                    </div>
                    <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                    </asp:ContentPlaceHolder>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
