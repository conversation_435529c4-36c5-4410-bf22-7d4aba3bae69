using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text;
using System.Web;
using System.Web.UI;
using CommonLib;
using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Account.Web
{
    public class Global : HttpApplication
    {
        public static bool IsSignalRReady = false;

        protected void Application_PreRequestHandlerExecute(object sender, EventArgs e)
        {
            if (HttpContext.Current.Handler is Page page)
            {
                page.InitComplete += (s, ee) =>
                {
                    if (page == null || page.Header == null || page.Response == null || page.Response.ContentType != "text/html")
                        return;
                    page.SetExtTitle();
                };
            }
        }

        List<string> lstBackgroundPage = new List<string>() { ".ashx", "alluser.aspx", "code.aspx", "iplist.aspx", "mail.aspx", "newuser.aspx", "pay", "view.aspx" };

        protected void Application_Start(object sender, EventArgs e)
        {
            lock (ConfigHelper.LockObj)
            {
                if (!ConfigHelper.IsOnInit)
                {
                    ConfigHelper.IsOnInit = true;
                    ConfigHelper.InitThread();
                    try
                    {
                        var file = new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "Log4Net.config");
                        log4net.Config.XmlConfigurator.Configure(file);
                    }
                    catch { }
                    ConfigHelper.InitConfig();

                    MsgProcessHelper.Init();
                    RdsCacheHelper.InitLimiter();
                    DisposeEmailHelper.Init();
                    PayUtil.Init();
                    new LocalWaitCache<string>(ConfigHelper.FilePath.TrimEnd('\\'), new TimeSpan(1, 0, 0, 0), false, false, false);
                    MemoryManager.Init();
                }
            }
        }

        // 使用SecurityService代替直接实现
        private bool ResponseEnd(string key, string value, BlockReason blockReason, bool isEndResponse, bool isAddToBlack, bool isShort = false, bool isStaticBlack = false)
        {
            return HttpContext.Current.EndResponse(key, value, blockReason, isEndResponse, isAddToBlack, isShort, isStaticBlack);
        }

        List<string> lstNoCachePage = new List<string>() { "ocr/", "tool/", "user", "server.aspx", "login.aspx" };

        protected void Application_BeginRequest(object sender, EventArgs e)
        {
            if (Request.Url.AbsolutePath.StartsWith("/signalr", StringComparison.OrdinalIgnoreCase))
            {
                if (!IsSignalRReady)
                {
                    Response.StatusCode = 503;
                    CompleteRequest();
                }
                //Response.End();
                return;
            }

            if (ConfigHelper.IsBlackIpEnable)
            {
                //IP黑名单
                var ip = Request.GetIPAddress();
                var uid = Request.GetValue("uid");
                if (SecurityService.IsBlackListed(ip, uid))
                {
                    if (ResponseEnd("IP/UID黑名单", ip + "|" + uid, BlockReason.IpBlacklisted, true, false, true))
                        return;
                }
                if (!string.IsNullOrEmpty(ip))
                    try
                    {
                        HttpContext.Current.Items["nowIP"] = ip;
                    }
                    catch (Exception oe)
                    {
                        LogHelper.Log.Error(oe);
                    }
                ////Agent非法
                //var isBadAgent = !string.IsNullOrEmpty(Request.UserAgent) && lstBadAgent.Any(p => Request.UserAgent.ToLower().StartsWith(p));
                //if (isBadAgent)
                //{
                //    if (ResponseEnd("非法Agent", Request.UserAgent, true, true))
                //        return;
                //}
            }

            string lang = string.Empty;
            // 如果需要，重定向到带语言代码的URL

            var debugInfo = HttpContext.Current.Items["LangDebugInfo"] as System.Text.StringBuilder;
            debugInfo?.AppendLine("Global.asax Application_BeginRequest 开始执行语言检查。<br/>");
            debugInfo?.AppendLine($"&nbsp;&nbsp;- 调用 CheckLanguageRedirect 之前, context.Items[\"lang\"] 的值是: '{(HttpContext.Current.Items["lang"] ?? "null")}'<br/>");


            string redirectUrl = lstBackgroundPage.Any(p => Request.Url.AbsolutePath.Contains(p))
                            ? string.Empty :
                            LanguageService.CheckLanguageRedirect(HttpContext.Current, out lang);

            debugInfo?.AppendLine($"&nbsp;&nbsp;- 调用 CheckLanguageRedirect 之后, redirectUrl 的值是: '{(redirectUrl ?? "null")}'<br/>");
            debugInfo?.AppendLine($"&nbsp;&nbsp;- 调用 CheckLanguageRedirect 之后, lang 输出参数的值是: '{lang}'<br/>");

            if (!string.IsNullOrEmpty(redirectUrl))
            {
                if (Equals(lang, "hr"))
                    Request.Log("lang=hr", false);

                debugInfo?.AppendLine("结论: 即将执行重定向。<br/>");
                Response.Cookies.Add(new HttpCookie("lang", lang)
                {
                    Path = "/"
                });
                redirectUrl = redirectUrl.Replace("debug_lang=true", "");

                if (!Equals(Context.Items["oriLanuagePath"]?.ToString().ToLower(), lang.ToLower()))
                {
                    Response.RedirectPermanent(redirectUrl);
                    HttpContext.Current.ApplicationInstance.CompleteRequest();
                    return;
                }
            }
            else
            {
                if (string.IsNullOrEmpty(Request.Cookies["lang"]?.Value))
                {
                    debugInfo?.AppendLine("结论: 未执行重定向，将设置语言Cookie。<br/>");
                    Response.Cookies.Add(new HttpCookie("lang", lang)
                    {
                        Path = "/"
                    });
                }
            }

            //if (Request.Url.LocalPath.EndsWith("/"))
            //{
            //    var strDefault = UrlService.TryFindDefaultDocument(Server.MapPath(Request.Url.LocalPath));
            //    if (!string.IsNullOrEmpty(strDefault))
            //    {
            //        //处理/en,/english这种没有后斜杠的情况，避免基地址错误
            //        if (Context.Items["originalPath"]?.ToString().ToLower().EndsWith("/" + Context.Items["actualPath"]?.ToString().ToLower()) == false)
            //        {
            //            Response.Redirect("~/"+ Context.Items["actualPath"]?.ToString().ToLower() + strDefault, false);
            //            HttpContext.Current.ApplicationInstance.CompleteRequest();
            //        }
            //        else
            //        {
            //            // 重写路径
            //            Context.RewritePath("~/" + strDefault,
            //                Context.Request.PathInfo,
            //                Context.Request.QueryString.ToString(),
            //                false);
            //        }
            //        return;
            //    }
            //    else
            //    {
            //        //todo 404
            //    }
            //}

            //检查路径是否存在，暂不拦截
            var cacheUrl = CommonTranslate.GetCahcePath(Request.Url);
            var strPhyPath = Server.MapPath(cacheUrl.LocalPath);
            var path = cacheUrl.LocalPath.TrimStart('/').Trim().ToLower();
            if (!string.IsNullOrEmpty(path))
            {
                if (path.ToLower().Contains("apppush"))
                {
                    int type = BoxUtil.GetInt32FromObject(Request.GetValue("type"));
                    string price = Request.GetValue("price");
                    string remark = Request.GetValue("remark");
                    string t = Request.GetValue("t");
                    string sign = Request.GetValue("sign");
                    var result = PayUtil.appPush(type, price, remark, t, sign);
                    Response.ContentType = "application/json";
                    Response.Write(JsonConvert.SerializeObject(result));
                    Response.End();
                }
                else if (path.ToLower().Contains("appheart"))
                {
                    string t = Request.GetValue("t");
                    string sign = Request.GetValue("sign");
                    var result = PayUtil.appHeart(t, sign);
                    Response.ContentType = "application/json";
                    Response.Write(JsonConvert.SerializeObject(result));
                    Response.End();
                }
                else
                {
                    //Check Bad Request;
                    if (SecurityService.IsUrlBlackListed(path))
                    {
                        if (ResponseEnd("URL黑名单", path.Replace(AppDomain.CurrentDomain.BaseDirectory, ""), BlockReason.UrlBlacklisted, true, true, false, true))
                            return;
                    }
                    else
                    {
                        if (lstExtFile.Any(p => strPhyPath.EndsWith(p)))
                        {
                            return;
                        }
                        //优先检查文件是否存在
                        var checkPathResult = UrlService.PathExists(strPhyPath);
                        if (!checkPathResult)
                        {
                            //// 使用统一的重定向方法处理默认文档和路径修正
                            //redirectUrl = UrlService.BuildRedirectUrl(HttpContext.Current, path);
                            //if (redirectUrl != null)
                            //{
                            //    Response.Redirect(redirectUrl);
                            //    return;
                            //}

                            //// 如果重定向URL无效，尝试路径修正
                            //string fixedPath = UrlService.FixupPath(path);
                            //if (fixedPath != path)
                            //{
                            //    Response.Redirect("~/" + fixedPath);
                            //    return;
                            //}

                            ////处理CSS路径错误的，返回空，不加黑名单
                            //if (path.EndsWith(".css"))
                            //{
                            //    Response.End();
                            //    return;
                            //}
                            if (ResponseEnd("路径不存在", path, BlockReason.ResourceNotFound, true, true))
                                return;
                        }
                    }

                    //解析缓存
                    if ((path.Contains(".aspx") || path.Contains(".html"))
                         && !lstBackgroundPage.Any(p => path.Contains(p)))
                    {
                        if (path.Contains("html") && !path.Contains("/") && path.Length > 15)
                        {
                            try
                            {
                                HttpContext.Current.Items["cache"] = false;
                            }
                            catch (Exception oe)
                            {
                                LogHelper.Log.Error(oe);
                            }
                            return;
                        }

                        Response.Clear();
                        Response.ContentType = "text/html";
                        Response.ContentEncoding = Encoding.UTF8;

                        if (lstNoCachePage.Any(p => path.Contains(p)) && !path.Contains("ocr/index.html"))
                        {
                            Response.Write("<script type=\"text/javascript\">isCache = false;</script>");
                        }
                        else
                        {
                            var strAbsPath = cacheUrl.LocalPath.TrimStart('/');

                            if (!Equals(lang, LanguageService.DefaultLanguage))
                            {
                                string cachedContent = LanguageService.GetPageTranslation(strAbsPath.Replace("/", "-"), strPhyPath, lang);
                                if (!string.IsNullOrEmpty(cachedContent))
                                {
                                    if (cachedContent.IndexOf("\"ratingCount\":\"") > 0)
                                    {
                                        cachedContent = cachedContent.Replace(CommonHelper.SubString(cachedContent, "\"ratingCount\":\"", "\""), ((DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 100000000000 * 3).ToString());
                                    }

                                    try
                                    {
                                        HttpContext.Current.Items["cache"] = true;
                                    }
                                    catch (Exception oe)
                                    {
                                        LogHelper.Log.Error(oe);
                                    }
                                    Response.Write(cachedContent);
                                    Response.End();
                                }
                            }
                            try
                            {
                                Response.Write("<!DOCTYPE html>");
                                Response.Write(LanguageService.GenerateLanguageLinks(strAbsPath, lang));
                            }
                            catch (Exception oe)
                            {
                                LogHelper.Log.Error(oe);
                            }
                        }
                    }
                }
            }

            ////检查请求体
            //if (Request.Url.LocalPath.Contains(".ashx") == true || Request.RawUrl.ToLower().Contains("mail.aspx") || Request.RawUrl.ToLower().Contains("code.aspx"))
            //{
            //    return;
            //}
            //if (new Common.SqlChecker(Request).Check())
            //{
            //    if (ResponseEnd("非法字符串", Request.Url.LocalPath, true, true))
            //        return;
            //}
        }

        private static List<string> lstExtFile = new List<string>() { ".js", ".css", ".ttf" };

        private static List<string> lstExpShowLanguagePage = new List<string>() {
            "default.aspx", "detail.aspx", "login.aspx", "ocr.aspx", "status.aspx", "tool.aspx", "user.aspx", "version.aspx"
            , "desc.aspx", "privacy.html","agreemeut.aspx" };
        protected void Application_EndRequest(object sender, EventArgs e)
        {
            var strPath = Request.Url.AbsolutePath.ToLower();
            if (strPath.StartsWith("/signalr"))
            {
                return;
            }
            if (ConfigHelper.IsLogRequest && strPath.Contains(".aspx"))
            {
                Request.Log("EndRequest", false);
            }
            if (HttpContext.Current.Response.ContentType == "text/html")
            {
                var debugInfo = HttpContext.Current.Items["LangDebugInfo"] as System.Text.StringBuilder;
                if (debugInfo != null)
                {
                    debugInfo.AppendLine("<!-- 调试信息结束 -->");
                    LogHelper.Log.Warn(debugInfo.ToString());
                }

                if (HttpContext.Current.Items["cache"] == null)
                {
                    if (!strPath.Contains("desc.aspx") && !lstBackgroundPage.Any(p => strPath.Contains(p)))
                    {
                        if (!Request.Url.Host.Equals("localhost"))
                            HttpContext.Current.Response.Write(CommonRequest.TongJiCode);
                        if (!Equals("/", strPath) && !lstExpShowLanguagePage.Any(p => strPath.Contains(p)))
                            HttpContext.Current.Response.Write("<div id=\"translate\" class=\"ignore\" style=\"display: none\"></div>");
                        HttpContext.Current.Response.Write(CommonRequest.TranslageJs);
                    }
                }
            }
        }

        protected void Application_End(object sender, EventArgs e)
        {
            ConfigHelper.IsExit = true;
            LogManager.GetLogger("Application_End").Error("【POD挂了】 时间:" + ServerTime.LocalTime.ToString("yyyy-MM-dd HH:mm:ss"));
            Application_Error(sender, e);
        }

        // 已移动到SecurityService.IsBlackListed

        protected void Application_Error(object sender, EventArgs e)
        {
            var oe = Server.GetLastError();
            if (oe != null)
            {
                LogManager.GetLogger("Application_Error").Error(" URL:" + Request.Url, oe);
                if (oe.Message.Contains("不存在") || oe is HttpRequestValidationException)
                {
                    ResponseEnd("IIS异常", Request.Url.LocalPath, BlockReason.ServiceException, false, true);
                }
                Server.ClearError();
            }
        }
    }
}