﻿<%@ Page Title="" Language="C#" CodeBehind="UserLogin.aspx.cs" Inherits="Account.Web.UserLogin" %>

<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="robots" content="nofollow,noarchive">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>用户登录<%=PageTitleConst.Default_Ext %></title>
    <link rel="stylesheet" href="./CSS/bootstrap.min.css">
</head>
<body>
    <div class="card-body">
        <div class="main">
            <form method="post" id="loginForm" action="UserLogin.aspx" runat="server">
                <div class="text-center m-b">
                    <h4 class="text-uppercase">用户登录</h4>
                    <p>AI智能一站式平台，专注提升生产力！</p>
                </div>
                <div class="form-group">
                    <input name="username" class="form-control input-lg email" placeholder="手机号/邮箱" value="<%=Request.QueryString["username"] %>" required="" />
                </div>
                <div class="form-group">
                    <input name="password" class="form-control input-lg password" type="password" placeholder="密码" value="<%=Request.QueryString["password"] %>" required="" />
                </div>
                <div class="form-group">
                    <div class="alert" style="display: none;"></div>
                </div>
                <div class="form-group">
                    <div class="col-xs-6 column">
                        <input type="checkbox" checked="checked" class="remember" title="记住我的状态" />
                        记住我的状态 
                    </div>
                </div>
                <button class="btn btn-primary btn-block btn-lg m-b" style="background-color: #034FD8; border-color: #034FD8;" type="submit" id="btnLogin">登 录</button>
                <p class="text-center" style="margin-top: 10px">
                    <a href="UserForgetPwd.aspx">忘记密码</a>&nbsp;&nbsp;&nbsp;<a href="UserReg.aspx">注册会员</a>
                </p>
            </form>
        </div>
    </div>
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//cdn.bootcdn.net/ajax/libs/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script src="https://static.geetest.com/v4/gt4.js"></script>
    <script>
        initGeetest4({
            captchaId: 'e47a14e55b2db9ce50dd5afa1a9952d8',
            product: 'bind',
            riskType: 'nine'
        }, function (captchaObj) {
            captchaObj.onSuccess(function () {
                var result = captchaObj.getValidate();
                if (result) {
                    document.getElementById('loginForm').submit();
                }
            });
            $("#loginForm").bind("submit", function () {
                var result = captchaObj.getValidate();
                if (result == undefined || !result) {
                    captchaObj.showCaptcha();
                    return false;
                }
                else {
                    return true;
                }
            });
        });
    </script>
</body>
</html>
