﻿using Account.Web.Common;
using CommonLib;
using System;
using System.Text.RegularExpressions;

namespace Account.Web
{
    public partial class UserForgetPwd : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.RequestType.Equals("POST"))
            {
                string account = Request.Form["account"];
                string password = Request.Form["password"];
                string verify = Request.Form["verify"];
                var lang = Request.GetValue("lang");

                if (string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password))
                {
                    Response.Write("<script>alert('" + UserConst.StrAccountEmpty.GetTrans(lang) + "');</script>");
                    return;
                }
                if (!new Regex(@"[0-9A-Za-z].{6,15}").IsMatch(password)) //判断密码格式是否符合要求
                {
                    Response.Write("<script>alert('" + UserConst.StrPwdFormatError.GetTrans(lang) + "');</script>");
                    return;
                }
                if (string.IsNullOrEmpty(verify))
                {
                    Response.Write("<script>alert('" + UserConst.StrValidateCodeEmpty.GetTrans(lang) + "');</script>");
                    return;
                }

                if (!BoxUtil.IsEmail(account) && !BoxUtil.IsMobile(account))
                {
                    Response.Write("<script>alert('" + UserConst.StrAccountFormatError.GetTrans(lang) + "');</script>");
                }
                else
                {
                    var user = CodeHelper.GetCodeByAccountId(account);
                    if (user == null || string.IsNullOrEmpty(user.StrAppCode))
                    {
                        // 用户不存在，显示错误提示
                        Response.Write("<script>alert('" + UserConst.StrAccountNotExsits.GetTrans(lang) + "');</script>");
                    }
                    else
                    {
                        var validateCode = BoxUtil.GetStringFromObject(verify);
                        var validateMd5 = CommonValidateCode.GetValidateCode(account, "OCRREGForgetPwd");
                        if (validateMd5.Equals(validateCode?.ToUpper()))
                        {
                            password = CommonValidateCode.GetMD5String(password + "OCRREG").ToUpper();
                            if (CodeHelper.UpdateCodePwd(account, password))
                            {
                                Response.Write("<script>alert('" + UserConst.StrResetPwdSuccess.GetTrans(lang) + "');</script>");
                            }
                            else
                            {
                                Response.Write("<script>alert('" + UserConst.StrServerError.GetTrans(lang) + "');</script>");
                            }
                        }
                        else
                        {
                            Response.Write("<script>alert('" + UserConst.StrValidateCodeError.GetTrans(lang) + "');</script>");
                        }
                    }
                }
            }
        }
    }
}