﻿<%@ Page Title="用户中心" Language="C#" MasterPageFile="~/UserMaster.Master" AutoEventWireup="true" CodeBehind="UserIndex.aspx.cs" Inherits="Account.Web.UserIndex" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script src="//cdn.bootcdn.net/ajax/libs/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript">
        function editNickName() {
            $("#showNickName").hide();
            $("#editNickName").css("display", "flex");
        }
        var nickNameReg = /^[\u4e00-\u9fa5\w]{2,12}$/;
        function saveNickName() {
            var nickName = $("#nickname").val();
            if (!nickNameReg.test(nickName)) {
                alert("昵称为2-12位的中英文数字字母或下划线！");
                return;
            }
            $("#lblNickName").text(nickName);
            $("#editNickName").hide();
            $("#showNickName").show();
            window.location = "UserIndex.aspx?op=updatenickname&nickname=" + nickName;
        }
        function editPwd() {
            if ($("#editPwd").is(':visible') == false) {
                $("#editPwd").show();
                return;
            }
            var password = $("#password").val();
            if (password == null || password.trim() == '') {
                alert('密码不能为空！');
                return
            }
            window.location = "UserIndex.aspx?op=updatepwd&pwd=" + password;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <div class="user_body">
        <div class="body_header">
            <div class="header_user">
                <div class="user_avatar">
                    <img class="mod-avatar-100" src="https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7131.**********-2023-06-05-*************.gif" alt="">
                </div>
                <div class="user_info">
                    <%
                        var session = Account.Web.UserLoginInfoHelper.GetUserInfo(Request, Response);
                    %>
                    <p class="info_name" id="showNickName">
                        <label id="lblNickName"><%=session==null?"": session.NickName %></label>
                        <a class="info_modify" href="javascript:editNickName()">修改昵称&gt;&gt;</a>
                    </p>
                    <p class="info_name" id="editNickName" style="display: none;">
                        <input type="text" class="form-control input-lg" id="nickname" placeholder="请输入您的昵称" value="<%=session==null?"":session.NickName %>" style="width: 200px">
                        <a class="info_modify" style="padding-left: 10px;" href="javascript:saveNickName()">保存昵称</a>
                    </p>
                    <p class="info_team">账号：<%=session==null?"":session.Account %></p>
                    <p class="info_contribute">
                        <span class="contribute_text">类型：</span>
                        <span class="contribute_num"><%=session==null?"":session.UserTypeName %></span>
                    </p>
                    <p class="info_signature">到期时间：<%=session==null?"":session.DtExpired.Year > 1900?session.DtExpired.ToString("yyyy-MM-dd"):"" %></p>
                    <p class="info_name" id="editPwd" style="display: none;">
                        <input type="password" class="form-control input-lg" id="password" placeholder="8-16位密码，大小写字母、数字、符号" style="width: 300px">
                    </p>
                    <a class="info_modify" href="javascript:editPwd();">修改密码 &gt;&gt;</a>
                </div>
                <%
                    var lstAllType = CommonLib.UserTypeHelper.GetCanRegUserTypes();
                    var nowUserType = (CommonLib.UserTypeEnum)Enum.Parse(typeof(CommonLib.UserTypeEnum), session.UserTypeName);
                    var nextType = lstAllType.FirstOrDefault(p => p.Type > nowUserType);
                    if (nextType == null)
                    {
                        nextType = lstAllType.FirstOrDefault(p => p.Type == nowUserType);
                    }
                    if (nextType == null)
                    {
                        nextType = lstAllType.FirstOrDefault();
                    }
                %>
                <a class="mod-btn mod-btn-blue" href="UserUpgrade.aspx?type=<%=nextType.Type.GetHashCode() %>">去升级</a>
            </div>
        </div>
        <div class="body_content">
            <ul class="content_list">

                <li class="list_item list_item-3" style="cursor: pointer;" onclick="window.location='UserMac.aspx'">
                    <i class="item_pic"></i>
                    <div class="item_content">
                        <p class="content_title"><span class="title_text">我的设备</span></p>
                        <a class="content_detail" href="UserMac.aspx">活动设备详情 &gt;&gt;</a>
                    </div>
                </li>

                <%--<li class="list_item list_item-2" style="cursor:pointer;">
                    <i class="item_pic"></i>
                    <div class="item_content">
                        <p class="content_title"><span class="title_text">我的反馈</span><span class="title_num">0</span><span class="title_text">个</span></p>
                        <a class="content_detail" href="javascript:void(0);">我的反馈记录 &gt;&gt;</a>
                    </div>
                </li>--%>

                <li class="list_item list_item-4" style="cursor: pointer;">
                    <i class="item_pic"></i>
                    <div class="item_content">
                        <p class="content_title"><span class="title_text">我的消息</span><span class="title_num">0</span><span class="title_text">条</span></p>
                        <a class="content_detail" href="javascript:void(0);">消息中心 &gt;&gt;</a>
                    </div>
                </li>

                <li class="list_item list_item-1" style="cursor: pointer;">
                    <i class="item_pic"></i>
                    <div class="item_content">
                        <p class="content_title"><span class="title_text">我的订单</span><span class="title_num">0</span></p>
                        <a class="content_detail" href="javascript:void(0);">查询历史订单 &gt;&gt;</a>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</asp:Content>
