﻿using Account.Web.Common;
using CommonLib;
using Newtonsoft.Json;
using System;
using System.Text.RegularExpressions;

namespace Account.Web
{
    public partial class UserReg : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.RequestType.Equals("POST"))
            {
                string account = Request.Form["account"];
                string password = Request.Form["password"];
                string verify = Request.Form["verify"];
                string nickName = Request.Form["nickname"];

                var lang = Request.GetValue("lang");
                if (string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password))
                {
                    Response.Write("<script>alert('" + UserConst.StrAccountEmpty.GetTrans(lang) + "');</script>");
                    return;
                }
                if (!new Regex(@"[0-9A-Za-z].{6,15}").IsMatch(password)) //判断密码格式是否符合要求
                {
                    Response.Write("<script>alert('" + UserConst.StrPwdFormatError.GetTrans(lang) + "');</script>");
                    return;
                }
                if (string.IsNullOrEmpty(verify))
                {
                    Response.Write("<script>alert('" + UserConst.StrValidateCodeEmpty.GetTrans(lang) + "');</script>");
                    return;
                }
                if (nickName.Length < 2 || nickName.Length > 12 ||
                    !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                {
                    Response.Write("<script>alert('" + UserConst.StrNickNameFormatError.GetTrans(lang) + "');</script>");
                    return;
                }

                var isEmail = BoxUtil.IsEmail(account);
                var isMobile = BoxUtil.IsMobile(account);
                if (!isEmail && !isMobile)
                {
                    Response.Write("<script>alert('" + UserConst.StrAccountFormatError.GetTrans(lang) + "');</script>");
                }
                else
                {
                    var user = CodeHelper.GetCodeByAccountId(account);
                    if (user != null && !string.IsNullOrEmpty(user.StrAppCode))
                    {
                        Response.Write("<script>alert('" + UserConst.StrHasReged.GetTrans(lang) + "');</script>");
                    }
                    else
                    {
                        var validateMd5 = CommonValidateCode.GetValidateCode(account, "OCRREG");
                        if (validateMd5.Equals(verify.ToUpper()))
                        {
                            CodeEntity code = CodeEntity.GetNewEntity(account, nickName, password);

                            if (isEmail && DisposeEmailHelper.IsDisposeEmail(code.StrAppCode))
                            {
                                LogHelper.Log.Info(string.Format("临时邮箱:{0}", JsonConvert.SerializeObject(code)));
                                code.StrType = UserTypeEnum.体验版.ToString();
                                code.DtExpire = code.DtReg;
                            }
                            else
                            {
                                if (ConfigHelper.IsRegToGeRen)
                                {
                                    code.StrType = UserTypeEnum.个人版.ToString();
                                    code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                                }
                                else if (ConfigHelper.IsRegToProfessional)
                                {
                                    code.StrType = UserTypeEnum.专业版.ToString();
                                    code.DtExpire = code.DtReg.AddDays(ConfigHelper.NRegSendDays);
                                }
                                else
                                {
                                    code.StrType = UserTypeEnum.体验版.ToString();
                                    code.DtExpire = code.DtReg;
                                }
                            }

                            if (CodeHelper.AddOrUpdateCode(code))
                            {
                                Response.Write("<script>alert('" + UserConst.StrRegSuccess.GetTrans(lang) + "');window.location='UserLogin.aspx'</script>");
                            }
                            else
                            {
                                Response.Write("<script>alert('" + UserConst.StrServerError.GetTrans(lang) + "');</script>");
                            }
                        }
                        else
                        {
                            Response.Write("<script>alert('" + UserConst.StrValidateCodeError.GetTrans(lang) + "');</script>");
                        }
                    }
                }
            }
        }
    }
}