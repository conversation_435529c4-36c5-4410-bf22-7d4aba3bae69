﻿using CommonLib;
using System.Text.RegularExpressions;

namespace Account.Web.Common
{
    public class CommonValidateCode
    {
        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GetValidateCode(string str, string type, bool isSub = true)
        {
            var md5 = GetMD5String(string.Format("{0}{1}{2}", str, ServerTime.LocalTime.Date.ToString("yyyy-MM-dd"), type));
            var result = Regex.Replace(md5, @"[^0-9]+", "").PadRight(6, '0');
            if (isSub)
            {
                result = result.Substring(0, 6);
            }
            return result;
        }

        /// <summary>
        /// 通过字符串获取MD5值，返回32位字符串。
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GetMD5String(string str)
        {
            using (var md5 = System.Security.Cryptography.MD5.Create())
            {
                byte[] data2 = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(str));

                return GetbyteToString(data2);
            }
        }

        private static string GetbyteToString(byte[] data)
        {
            var sb = new System.Text.StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            return sb.ToString();
        }
    }
}