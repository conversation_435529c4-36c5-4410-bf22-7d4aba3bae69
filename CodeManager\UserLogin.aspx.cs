﻿using CommonLib;
using System;

namespace Account.Web
{
    public partial class UserLogin : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.RequestType.Equals("POST"))
            {
                string username = Request.Form["username"];
                string password = Request.Form["password"];
                var lang = Request.GetValue("lang");

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    Response.Write("<script>alert('" + UserConst.StrAccountEmpty.GetTrans(lang) + "');</script>");
                }
                else if (!BoxUtil.IsEmail(username) && !BoxUtil.IsMobile(username))
                {
                    Response.Write("<script>alert('" + UserConst.StrAccountFormatError.GetTrans(lang) + "');</script>");
                }
                else
                {
                    Server.Transfer("Code.aspx?op=login&account=" + username + "&pwd=" + password + "&isweb=1");
                }
            }
        }
    }
}