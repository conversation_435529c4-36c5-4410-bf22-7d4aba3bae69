﻿using Account.Web.Common;
using System;
using System.Text.RegularExpressions;

namespace Account.Web
{
    public partial class UserIndex : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (Equals(Request.QueryString["op"], "logout"))
            {
                UserLoginInfoHelper.LoginOut(Context);
            }
            var session = UserLoginInfoHelper.GetUserInfo(Request, Response);
            string strOp = Request.QueryString["op"];
            if (Request.RequestType.Equals("GET") && !string.IsNullOrEmpty(strOp))
            {
                string account = session.Account;
                switch (strOp)
                {
                    case "updatepwd":
                        var password = Request.QueryString["pwd"];
                        if (string.IsNullOrEmpty(password) || !new Regex(@"[0-9A-Za-z].{6,15}").IsMatch(password)) //判断密码格式是否符合要求
                        {
                            Response.Write("<script>alert('密码必须为6-15位的数字或大小写字母！');</script>");
                            return;
                        }
                        password = CommonValidateCode.GetMD5String(password + "OCRREG").ToUpper();
                        if (CodeHelper.UpdateCodePwd(account, password))
                        {
                            UserLoginInfoHelper.LoginOut(Context);
                            Response.Write("<script>alert('密码修改成功，请使用新密码登录！');window.location='UserIndex.aspx'</script>");
                        }
                        else
                        {
                            Response.Write("<script>alert('修改密码失败，请联系客服协助！');</script>");
                        }
                        break;
                    case "updatenickname":
                        string nickName = Request.QueryString["nickname"];
                        if (nickName.Length < 2 || nickName.Length > 12 ||
                            !new Regex(@"^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z0-9_])").IsMatch(nickName))
                        {
                            Response.Write("<script>alert('昵称为2-12位的中英文数字字母或下划线！');</script>");
                            return;
                        }
                        if (CodeHelper.UpdateCodeNickName(account, nickName))
                        {
                            session.NickName = nickName;
                            Response.Write("<script>alert('昵称修改成功！');</script>");
                        }
                        else
                        {
                            Response.Write("<script>alert('修改昵称失败，请联系客服协助！');</script>");
                        }
                        break;
                    default:
                        break;
                }

            }
        }
    }
}