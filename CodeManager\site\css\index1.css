/*
* Prefixed by https://autoprefixer.github.io
* PostCSS: v8.4.14,
* Autoprefixer: v10.4.7
* Browsers: last 4 version
*/
body {
  overflow-y: scroll;
  scroll-behavior: smooth;
}

@-webkit-keyframes shake {
  10%,
  90% {
    -webkit-transform: translate3d(-3px, 0, 0);
    transform: translate3d(-3px, 0, 0);
  }
  20%,
  80% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  30%,
  50%,
  70% {
    -webkit-transform: translate3d(-7px, 0, 0);
    transform: translate3d(-7px, 0, 0);
  }
  40%,
  60% {
    -webkit-transform: translate3d(9px, 0, 0);
    transform: translate3d(9px, 0, 0);
  }
}
@keyframes shake {
  10%,
  90% {
    -webkit-transform: translate3d(-3px, 0, 0);
    transform: translate3d(-3px, 0, 0);
  }
  20%,
  80% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  30%,
  50%,
  70% {
    -webkit-transform: translate3d(-7px, 0, 0);
    transform: translate3d(-7px, 0, 0);
  }
  40%,
  60% {
    -webkit-transform: translate3d(9px, 0, 0);
    transform: translate3d(9px, 0, 0);
  }
}
.shake {
  -webkit-animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

@media screen and (min-width: 992px) {
  .v4_header_mob {
    display: none !important;
  }
}
@media screen and (max-width: 991px) {
  .v4_header_pc {
    display: none !important;
  }
}

.v4_header_pc {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: transparent;
  z-index: 999;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-transition: background-color 0.3s ease;
  -o-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid transparent;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
}
.v4_header_pc.active {
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: saturate(180%) blur(0px);
  backdrop-filter: saturate(180%) blur(0px);
  border-bottom-color: #eceff4;
}
.v4_header_pc.scrollActive {
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: saturate(180%) blur(0px);
  backdrop-filter: saturate(180%) blur(0px);
  border-bottom: 1px solid #e1e6ed;
}

.sg-data {
  position: absolute;
}
.v4_header_pc .header_pc_left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
}
.v4_header_pc .pc-logo-wrap {
  height: 32px;
}
.v4_header_pc .pc-logo-wrap img.pc-logo {
  width: auto;
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}
.v4_header_pc .pc-logo-wrap img.pc-logo-white {
  height: auto;
  -o-object-fit: contain;
  object-fit: contain;
  display: none;
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .v4_header_pc .pc-logo-wrap {
    margin-left: 12px !important;
    margin-right: 12px !important;
    height: 24px;
  }
}
.v4_header_pc ul.top-nav {
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
}
.v4_header_pc ul.top-nav li {
  width: 120px;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
}
.v4_header_pc ul.top-nav li > a {
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transform: color 0.3s;
  -ms-transform: color 0.3s;
  transform: color 0.3s;
}
.v4_header_pc ul.top-nav li > a.active {
  color: #1764ff !important;
}
.v4_header_pc .nav-item-icon {
  font-size: 12px;
  color: #272636;
  font-weight: 300;
}
.v4_header_pc .sub-nav-wrap {
  position: absolute;
  top: 60px;
  left: 0;
  width: 100%;
  z-index: 999;
}
.v4_header_pc .sub-nav-content {
  position: absolute;
  top: 0;
  left: 0;
  height: 0;
  width: 100%;
  -webkit-transition: height 0.3s ease;
  -o-transition: height 0.3s ease;
  transition: height 0.3s ease;
  -webkit-box-shadow: 0px 6px 6px rgb(0 0 0 / 5%);
  box-shadow: 0px 6px 6px rgb(0 0 0 / 5%);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.98);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px);
}
.v4_header_pc .sub-nav {
  padding-bottom: 72px;
}
.v4_header_pc .sub-nav-item {
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  cursor: pointer;
  margin-top: 20px;
}
.v4_header_pc .sub-nav-item-img {
  width: 26px;
  height: 26px;
  -o-object-fit: contain;
  object-fit: contain;
}
.v4_header_pc .sub-jiantou {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
  position: relative;
  right: 5px;
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.v4_header_pc .letter-wrap {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  line-height: 1;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: absolute;
  z-index: 9;
}

.v4_header_pc .sub-nav-item:hover {
  background-color: #eaf2ff;
  color: #0056b3;
}
.v4_header_pc .sub-nav-item:hover .sub-jiantou {
  opacity: 1;
  right: 0;
}

.v4_header_pc .header_pc_right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
  height: 60px;
}
.v4_header_pc .pc_control,
.v4_header_pc .pc_register {
  height: 100%;
  min-width: 150px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 16px;
  border-radius: 0;
}

/* 白色顶部-pc */
.v4_header_pc.whiteHeader,
.v4_header_pc.whiteHeader .iconfont4,
.v4_header_pc.whiteHeader ul.top-nav li > a.color-default,
.v4_header_pc.whiteHeader .pc_login {
  color: #fff;
}
.v4_header_pc.scrollActive.whiteHeader,
.v4_header_pc.scrollActive.whiteHeader .iconfont4,
.v4_header_pc.scrollActive.whiteHeader ul.top-nav li > a.color-default,
.v4_header_pc.scrollActive.whiteHeader .pc_login {
  color: #282b31;
}
.v4_header_pc.whiteHeader .pc-logo {
  display: none;
}
.v4_header_pc.whiteHeader .pc-logo-wrap img.pc-logo-white {
  display: block;
}
.v4_header_pc.scrollActive.whiteHeader .pc-logo {
  display: block;
}
.v4_header_pc.scrollActive.whiteHeader .pc-logo-wrap img.pc-logo-white {
  display: none;
}
.v4_header_pc.whiteHeader.active {
  background: #0b1d2d;
  border-color: #0b1d2d;
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
}
.v4_header_pc.whiteHeader.scrollActive {
  background: rgba(255, 255, 255, 0.96);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  -webkit-box-shadow: 0 0 5px #888;
  box-shadow: 0 0 5px #888;
  border-color: rgba(255, 255, 255, 0.96);
}

@media screen and (min-width: 1200px) and (max-width: 1399px) {
  .v4_header_pc .web-logo-wrap {
    margin-right: 12px !important;
    margin-left: 12px !important;
    height: 28px;
  }
  .v4_header_pc ul.top-nav li {
    width: 110px;
  }
  .v4_header_pc .pc_login {
    margin-right: 24px !important;
    padding-right: 12px !important;
  }
  .v4_header_pc .pc_control,
  .v4_header_pc .pc_register {
    min-width: 130px;
  }
  .v4_header_pc .nav-item-icon {
    margin-left: 4px !important;
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  .v4_header_pc .web-logo-wrap {
    margin-right: 12px !important;
    margin-left: 12px !important;
    height: 24px;
  }
  .v4_header_pc ul.top-nav li {
    width: 98px;
  }
  .v4_header_pc .pc_login {
    margin-right: 24px !important;
    padding-right: 0 !important;
  }
  .v4_header_pc .pc_control,
  .v4_header_pc .pc_register {
    min-width: 120px;
  }
  .v4_header_pc .nav-item-icon {
    margin-left: 4px !important;
  }
}

.v4_header_mob {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 54px;
  background-color: transparent;
  z-index: 999;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-transition: background-color 0.5s ease;
  -o-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
}
.v4_header_mob.active {
  background: rgba(255, 255, 255, 0.98);
}
.v4_header_mob.scrollActive {
  background: rgba(255, 255, 255, 0.98);
  -webkit-box-shadow: 0 0 5px #888;
  box-shadow: 0 0 5px #888;
}
.v4_header_mob.active .mob-nav-content {
  background-color: rgba(255, 255, 255, 0.98);
}
.v4_header_mob .mob-logo-wrap {
  height: 32px;
}
.v4_header_mob .mob-logo,
.v4_header_mob .mob-logo-white {
  height: 100%;
  width: auto;
  -o-object-fit: contain;
  object-fit: contain;
}
.v4_header_mob .mob-logo-white {
  display: none;
}
.v4_header_mob .right-menu {
  width: 50px;
  height: 54px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}
.v4_header_mob .right-menu .line {
  width: 30px;
  height: 2px;
  background-color: #000;
  border-radius: 1px;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  opacity: 1;
}
.v4_header_mob .right-menu .line::after,
.v4_header_mob .right-menu .line::before {
  content: "";
  width: 100%;
  height: 2px;
  background-color: #000;
  position: absolute;
  left: 0;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border-radius: 1px;
}
.v4_header_mob .right-menu .line::after {
  top: 8px;
}
.v4_header_mob .right-menu .line::before {
  bottom: 8px;
}

.v4_header_mob .right-menu.active .line {
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
  opacity: 1;
}
.v4_header_mob .right-menu.active .line::before {
  -webkit-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
  opacity: 1;
  bottom: 0;
}
.v4_header_mob .right-menu.active .line::after {
  opacity: 0;
}

.v4_header_mob .mob-nav-content {
  position: absolute;
  top: 53.5px;
  left: 0;
  width: 100%;
  z-index: 999;
  /* -webkit-backdrop-filter: saturate(180%) blur(20px);
  backdrop-filter: saturate(180%) blur(20px); */
  max-height: calc(100vh - 54px - 40px);
  overflow-y: scroll;
  -webkit-box-shadow: 0px 15px 15px 0px rgba(0, 17, 86, 0.05);
  box-shadow: 0px 15px 15px 0px rgba(0, 17, 86, 0.05);
  background: rgba(255, 255, 255, 0);
  -webkit-transition: background-color 0.5s ease;
  -o-transition: background-color 0.5s ease;
  transition: background-color 0.5s ease;
}

.v4_header_mob .nav-header-letter {
  height: 48px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #282b31;
}
.v4_header_mob .nav-header-letter:hover {
  color: #282b31;
}

.v4_header_mob .nav-header-letter .nav-header-jiantou {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
}
.v4_header_mob .sub-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.v4_header_mob .sub-nav-item > a {
  color: #282b31;
  border-bottom: 1px solid #b2bbd0;
  line-height: 1.5;
  display: inline-block;
}

.v4_header_mob .nav-header-letter[aria-expanded="true"] .nav-header-jiantou {
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.v4_header_mob .mob_control {
  min-width: 120px;
  height: 36px;
  line-height: 36px;
}

.v4_header_mob .mob_login {
  display: block;
  /* width: 100%; */
  width: 200px;
  text-align: center;
  font-weight: bold;
  height: 40px;
  line-height: 40px;
  color: inherit;
  color: #1764ff;
  border: 1px solid #1764ff;
  border-radius: 2px;
}
.v4_header_mob .mob_register {
  /* background-color: #ff227a !important; */
  background-color: #1764ff;
  /* width: 100%; */
  width: 200px;
  height: 40px;
  line-height: 40px;
  font-weight: bold;
  font-size: 16px;
}
/* 白色顶部-mob */
.v4_header_mob.whiteHeader.active,
.v4_header_mob.whiteHeader.active .mob-nav-content {
  background-color: #0b1d2d;
}

.v4_header_mob.whiteHeader,
.v4_header_mob.whiteHeader .nav-header-letter {
  color: #fff;
}
.v4_header_mob.whiteHeader .mob-logo {
  display: none;
}
.v4_header_mob.whiteHeader .mob-logo-white {
  display: block;
}
.v4_header_mob.whiteHeader .right-menu .line,
.v4_header_mob.whiteHeader .right-menu .line::after,
.v4_header_mob.whiteHeader .right-menu .line::before {
  background-color: #fff;
}
.v4_header_mob.whiteHeader .mob_register {
  color: #ff227a !important;
  background-color: #fff !important;
}

.v4_header_mob.whiteHeader.scrollActive.active,
.v4_header_mob.whiteHeader.scrollActive.active .mob-nav-content {
  background-color: rgba(255, 255, 255, 0.96);
}
.v4_header_mob.whiteHeader.scrollActive,
.v4_header_mob.whiteHeader.scrollActive .nav-header-letter {
  color: inherit;
}
.v4_header_mob.whiteHeader.scrollActive .mob-logo {
  display: block;
}
.v4_header_mob.whiteHeader.scrollActive .mob-logo-white {
  display: none;
}
.v4_header_mob.whiteHeader.scrollActive .right-menu .line,
.v4_header_mob.whiteHeader.scrollActive .right-menu .line::after,
.v4_header_mob.whiteHeader.scrollActive .right-menu .line::before {
  background-color: #000;
}
.v4_header_mob.whiteHeader.scrollActive .mob_register {
  color: #fff !important;
  background-color: #ff227a !important;
}

/* fixed-icon */
.fixed-icon-v4 {
  position: fixed;
  bottom: 18%;
  right: 3%;
  z-index: 999;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.fixed-icon-v4 .online-serve {
  width: 56px;
  height: 56px;
  cursor: pointer;
  background: #fff;
  position: relative;
  display: block;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease-in 0s;
  -o-transition: all 0.3s ease-in 0s;
  transition: all 0.3s ease-in 0s;
  border: 2px solid #fff;
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  -webkit-box-shadow: 3px 3px 10px 0px rgba(0, 17, 86, 0.1);
  box-shadow: 3px 3px 10px 0px rgba(0, 17, 86, 0.1);
  background-image: linear-gradient(0deg, #fff, #f3f5f8);
}

.fixed-icon-v4 .other-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 56px;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  background-image: -ms-linear-gradient(
    90deg,
    rgb(255, 255, 255) 0%,
    rgb(243, 245, 249) 100%
  );
  -webkit-box-shadow: 3px 3px 10px 0px rgba(0, 17, 86, 0.1);
  box-shadow: 3px 3px 10px 0px rgba(0, 17, 86, 0.1);
  border: 2px solid #fff;
  border-radius: 28px;
  padding: 12px 0;
  margin-top: 12px;
  background: #fff;
  background-image: linear-gradient(0deg, #fff, #f3f5f8);
}
.fixed-icon-v4 .other-icon .other-item {
  width: 100%;
  height: 52px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: relative;
}

.fixed-icon-v4 .other-icon .other-img {
  width: 32px;
  height: 32px;
  -o-object-fit: contain;
  object-fit: contain;
}

.fixed-icon-v4 .wx-text {
  position: absolute;
  left: -135px;
  top: 10px;
  opacity: 0;
  -webkit-transition: all 0.3s;
  -o-transition: all 0.3s;
  transition: all 0.3s;
  padding: 8px;
  font-weight: normal;
  color: #fff;
  border-radius: 4px;
  background: #000;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  width: 120px;
  text-align: center;
}

.fixed-icon-v4 .online-serve:hover .wx-text,
.fixed-icon-v4 .other-item:hover .wx-text {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.pls-nav-dropdown {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100vw;
  height: 580px;
  -webkit-transition: height 0.3s ease-in-out;
  -o-transition: height 0.3s ease-in-out;
  transition: height 0.3s ease-in-out;
  max-height: calc(100vh - 60px);
  overflow: hidden;
  background-color: #f5f7fb;
  -webkit-box-shadow: 0px 10px 20px -10px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 10px 20px -10px rgba(0, 0, 0, 0.3);
  border-top: 1px solid #e1e6ed;
}

.p-icon {
  width: 70px;
}
.contact-business {
  display: inline-block;
  margin-top: 3rem;
  color: #1764ff;
}
.contact-business:hover {
  color: #1764ff;
}
.seek-phone {
  margin-top: 8rem;
}
.prdocu-sub-left > div {
  margin-top: 2.5rem;
  margin-left: 12rem;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.prdocu-sub-left,
.prdocu-sub-right {
  overflow: scroll;
  padding-bottom: 3rem;
}
.prdocu-sub-left::-webkit-scrollbar,
.prdocu-sub-right::-webkit-scrollbar {
  width: 0px;
}
.prdocu-sub-left::-webkit-scrollbar-thumb,
.prdocu-sub-right::-webkit-scrollbar-thumb {
  display: none;
}
.prdocu-sub-right {
  width: 77%;
  padding-left: 30px;
}
.prdocu-sub-left {
  width: 23%;
  padding-right: 4rem;
  background-size: 60% 60%;
  background-repeat: no-repeat;
  background-position: 100% 100%;
}
.sub-left-desc {
  line-height: 26px;
}
.bg-white {
  background-color: #fff;
}

.text-base-color {
  color: #1764ff;
}
.pro-item {
  border: 1px solid #e1e6ed;
  width: 280px;
  height: 130px;
  padding: 24px 22px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(#f3f5f9),
    to(#ffffff)
  );
  background: -o-linear-gradient(top, #f3f5f9, #ffffff);
  background: linear-gradient(to bottom, #f3f5f9, #ffffff);
}
.pro-item:hover {
  border: 1px solid #ced4dd;
}

.pro-item h6 {
  font-size: 18px;
}
.pro-item-img {
  position: absolute;
  top: 35%;
  left: 45%;
  -webkit-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.pro-item:hover .pro-item-img {
  top: 25%;
  left: 35%;
}
.pro-item:hover .pro-vocie-img {
  top: 17% !important;
  left: 30% !important;
}
.pro-item:hover .pro-mail-img {
  top: -13% !important;
  left: 60% !important;
}
.pro-item:hover .pro-short-img {
  top: 12% !important;
  left: 51% !important;
}
.pro-item:hover .pro-inter-img {
  top: 13% !important;
  left: 48% !important;
}
.pro-item:hover .pro-smart-img {
  top: -3% !important;
  left: 53% !important;
}
.pro-item:hover .pro-onepass-img {
  top: 14% !important;
  left: 45% !important;
}

.pro-item:hover .pro-factor-img {
  top: 4% !important;
  left: 35% !important;
}
.pro-item:hover .pro-rcs-img {
  top: 15% !important;
  left: 29% !important;
}
.pro-item:hover .pro-mms-img {
  top: 7% !important;
  left: 45% !important;
}
.prdocu-sub-right > div {
  margin-top: 2.5rem;
}

.prdocu-sub-right a {
  display: block;
  margin: 0 20px 20px 0;
}
.prdocu-sub-right a:hover {
  color: #1764ff;
}

.prdocu-sub-left .gt {
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: relative;
  top: 0;
  left: 0;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
  display: inline-block;
  font-weight: 300;
  font-size: 14px;
}
.prdocu-sub-left a.link:hover .gt {
  left: 3px;
}

.sol-img {
  width: 64px;
  height: 24px;
}
.solution-part .letter-wrap {
  position: static;
}
.ty-sol {
  color: #8a909d;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 50%;
}
.triangle {
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-top-color: black;
  border-bottom: none;
  -webkit-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.triangle.active {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
  border-top-color: #1764ff;
}

@media screen and (max-width: 1400px) {
  .prdocu-sub-left > div {
    margin-left: 2rem !important;
  }
}

@media screen and (max-width: 1920px) {
  .prdocu-sub-left > div {
    margin-left: 5rem;
  }
}

/* menu */
nav-products {
  height: 100%;
  width: 60px;
  position: relative;
  z-index: 10;
}
nav-products nav-button {
  width: 60px;
  height: 100%;
  display: inline-block;
  position: relative;
  z-index: 9;
}
nav-products nav-button > label {
  display: block;
  position: absolute;
  top: 2px;
  left: 5px;
  width: 51px;
  height: 51px;
  cursor: pointer;
  -webkit-transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
    -webkit-transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
    -webkit-transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
    transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
    transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99),
    -webkit-transform 0.4s cubic-bezier(0.4, 0.01, 0.165, 0.99);
}
nav-products nav-button:hover > label .nav-products-icon {
  background: #006bfc;
}
nav-products nav {
  width: 230px;
  padding: 0;
  display: flex;
  position: fixed;
  top: 0px;
  z-index: 7;
  height: 65px;
  padding-top: 0px;
  display: flex;
  align-items: start;
  align-content: flex-start;
  flex-wrap: wrap;
  border-radius: 0rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0) !important;
  transition: 0.2s cubic-bezier(0.935, 0.01, 0.27, 1) all;
  border-bottom: 1px solid #e7e7e7;
}
nav-products nav nav-item {
  flex: 0 0 25%;
  max-width: 25%;
  text-align: center;
  opacity: 0;
  pointer-events: none;
  -webkit-transform: scale(1.1) translateY(-24px);
  -ms-transform: scale(1.1) translateY(-24px);
  transform: scale(1.1) translateY(-24px);
  -webkit-transition: opacity 0.15s ease-out, -webkit-transform 0.15s ease-out;
  transition: opacity 0.15s ease-out, -webkit-transform 0.15s ease-out;
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  transition: opacity 0.15s ease-out, transform 0.15s ease-out,
    -webkit-transform 0.15s ease-out;
}
nav-products nav nav-item a {
  display: block;
  color: #393e49;
}
nav-products nav nav-item a:hover {
  text-decoration: none;
  color: #006bfc;
}
nav-products nav nav-item a:hover i {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transform: scale3d(1.05, 1.05, 1);
}
nav-products nav nav-item a.disabled {
  opacity: 0.5;
}
nav-products nav nav-item i {
  width: 55px;
  height: 55px;
  display: inline-block;
  border-radius: 50%;
  background: #dce0e9;
  border: none;
  margin-bottom: 0.5rem;
  transform: scale3d(1, 1, 1);
  transition: all 0.2s cubic-bezier(0.47, 1.96, 0.54, 1.58);
}
nav-products nav nav-item i svg {
  width: 38px;
  fill: #fff;
  padding-top: 8px;
}
nav-products.active1 {
  background: #1764ff;
}
nav-products.active1 .nav-products-icon {
  background: #fff;
}
nav-products.active1:hover .nav-products-icon {
  background: #fff;
}

nav-products.active1 > nav-button > label {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
nav-products.active1 > nav-button .mdi-nav-products-1 {
  -webkit-transition: -webkit-transform 0.25s 0.2s
    cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: -webkit-transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99),
    -webkit-transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
nav-products.active1 > nav-button .mdi-nav-products-1 .nav-products-icon {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}
nav-products.active1 > nav-button .mdi-nav-products-2 .nav-products-icon {
  opacity: 0;
}
nav-products.active1 > nav-button .mdi-nav-products-3 {
  -webkit-transition: -webkit-transform 0.25s 0.2s
    cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: -webkit-transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99),
    -webkit-transform 0.25s 0.2s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
nav-products.active1 > nav-button .mdi-nav-products-3 .nav-products-icon {
  -webkit-transition: -webkit-transform 0.2s;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}
nav-products.active1 nav {
  width: 540px;
  height: 517px;
  padding: 2rem;
  padding-top: 90px;
  border-radius: 1rem !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
nav-products.active1 nav nav-item {
  opacity: 1;
  pointer-events: auto;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
nav-products.active1 nav nav-item:nth-child(0),
nav-products.active1 nav nav-item:nth-child(1),
nav-products.active1 nav nav-item:nth-child(2),
nav-products.active1 nav nav-item:nth-child(3) {
  transition-delay: 200ms;
}
nav-products.active1 nav nav-item:nth-child(4),
nav-products.active1 nav nav-item:nth-child(5),
nav-products.active1 nav nav-item:nth-child(6),
nav-products.active1 nav nav-item:nth-child(7) {
  transition-delay: 230ms;
}
nav-products.active1 nav nav-item:nth-child(8),
nav-products.active1 nav nav-item:nth-child(9),
nav-products.active1 nav nav-item:nth-child(10),
nav-products.active1 nav nav-item:nth-child(11) {
  transition-delay: 260ms;
}
nav-products.active1 nav nav-item:nth-child(12),
nav-products.active1 nav nav-item:nth-child(13),
nav-products.active1 nav nav-item:nth-child(14),
nav-products.active1 nav nav-item:nth-child(15) {
  transition-delay: 290ms;
}
nav-products.active1 nav nav-item:nth-child(16),
nav-products.active1 nav nav-item:nth-child(17),
nav-products.active1 nav nav-item:nth-child(18),
nav-products.active1 nav nav-item:nth-child(19) {
  transition-delay: 320ms;
}
.sidebar-fix {
  display: none;
}
.sidebar-fix.show {
  display: block;
}
.mob-nav-item.active span {
  font-weight: bold;
  color: #1764ff;
}
.fixedActiveBg {
  position: fixed;
  top: 54px;
  left: 0;
  bottom: 0;
  right: 0;
  overflow-y: scroll;
  background: #f5f7fb;
  border-top: 1px solid #e1e6ed;
}
.sidebar-fix-left,
.sidebar-fix-rigth {
  height: 86vh;
  padding-top: 14px;
}
.sidebar-fix-rigth .sub-nav {
  display: none;
}
.sidebar-fix-rigth .sub-nav:nth-child(1) {
  display: block;
}

.mdi-nav-products-1,
.mdi-nav-products-2,
.mdi-nav-products-3 {
  width: 51px;
  height: 51px;
  display: block;
  position: absolute;
  top: 0px;
  left: 0px;
  -webkit-transition: -webkit-transform 0.25s
    cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: -webkit-transform 0.25s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: transform 0.25s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  transition: transform 0.25s cubic-bezier(0.4, 0.01, 0.165, 0.99),
    -webkit-transform 0.25s cubic-bezier(0.4, 0.01, 0.165, 0.99);
  -webkit-transform: rotate(0);
  -ms-transform: rotate(0);
  transform: rotate(0);
}

.mdi-nav-products-1 .nav-products-icon {
  -webkit-transition: -webkit-transform 0.2s 0.2s;
  transition: -webkit-transform 0.2s 0.2s;
  transition: transform 0.2s 0.2s;
  transition: transform 0.2s 0.2s, -webkit-transform 0.2s 0.2s;
  -webkit-transform: translateY(-5px);
  -ms-transform: translateY(-5px);
  transform: translateY(-5px);
}

.mdi-nav-products-2 .nav-products-icon {
  transition: 0.2s cubic-bezier(0.935, 0.01, 0.27, 1) opacity;
}

.mdi-nav-products-3 .nav-products-icon {
  -webkit-transition: -webkit-transform 0.2s 0.2s;
  transition: -webkit-transform 0.2s 0.2s;
  transition: transform 0.2s 0.2s;
  transition: transform 0.2s 0.2s, -webkit-transform 0.2s 0.2s;
  -webkit-transform: translateY(5px);
  -ms-transform: translateY(5px);
  transform: translateY(5px);
}

.nav-products-icon {
  width: 23px;
  height: 1px;
  background: #49505f;
  top: 25px;
  left: 14px;
  position: absolute;
}

/*  */
