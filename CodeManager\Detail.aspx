<%@ Page Title="OCR助手产品介绍" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="OCR助手产品详细介绍，了解文字识别、公式识别、表格提取、多语言翻译等全场景智能OCR功能。支持桌面和网页版，多个引擎选择，高识别率。" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0" />
    <link rel="preconnect" href="https://lsw-fast.lenovo.com.cn" crossorigin />
    <script type="application/ld+json">{"@context":"http://schema.org","@type":"SoftwareApplication","name":"OCR Assistant","description":"OCR Assistant is an efficient productivity tool that integrates text, tables, formulas, documents, and translation.","category":"Productivity","applicationCategory":"Business","image":"https://lsw-fast.lenovo.com.cn/appstore/apps/adp/logo/7273-2024-06-05074650-1717588010656.gif","screenshot":["https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/3441-2023-07-18051430-1689671670795.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/4112-2023-07-18051443-1689671683656.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/0653-2023-07-18051453-1689671693673.png","https://lsw-fast.lenovo.com.cn/appstore/apps/adp/img/6000-2023-07-18051502-1689671702366.png"],"aggregateRating":{"@type":"AggregateRating","worstRating":"1","bestRating":"5","ratingValue":"4.8","ratingCount":"<%=((System.DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / *************3).ToString() %>"},"offers":{"@type":"Offer","price":0,"priceCurrency":"USD","category":"free"},"operatingSystem": "Windows"}</script>

    <!-- 引入Product.aspx中的CSS和JS资源 -->
    <script src="/static/js/autocdn.js" type="text/javascript"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/log.min.js"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/common_sdk.min.js"></script>
    <link rel="stylesheet" type="text/css" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/common_new.css?1=1">
    <link rel="stylesheet" type="text/css" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/index_new.css">
    <link rel="stylesheet" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/style.*************.css" type="text/css" media="all">
    <link rel="stylesheet" href="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/css/styles.css" type="text/css" media="all">
    <script src="//cdn.bootcdn.net/ajax/libs/jquery/1.4.2/jquery.min.js" type="text/javascript" onerror="autoRetry(this)"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/body.*************.js"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/jquery.SuperSlide.2.1.1.js"></script>

    <!-- Product页面的内联样式 -->
    <style type="text/css">
        .mod-btn {
            display: inline-block;
            box-sizing: border-box;
            width: 140px;
            height: 40px;
            line-height: 40px;
            padding: 0;
            border: 1px solid transparent;
            border-radius: 3px;
            font-size: 15px;
            text-align: center;
            cursor: pointer;
            -webkit-transition: border-color .2s, color .2s, background-color .2s;
            transition: border-color .2s, color .2s, background-color .2s;
        }

        .mod-btn-blue {
            color: #fff;
            background-color: #007cfa;
        }

            .mod-btn-blue:hover {
                background-color: #3396fb;
            }

            .mod-btn-blue:focus {
                background-color: #0064ed;
            }

        .mod-btn-black {
            color: #666;
            background-color: #fff;
            border-color: #ededed;
        }

            .mod-btn-black:hover {
                color: #007cfa;
                border-color: #007cfa;
            }

        .mod-btn-white {
            color: #007cfa;
            border-color: #007cfa;
            background-color: #fff;
        }

            .mod-btn-white:hover {
                color: #fff;
                border-color: #3396fb;
                background-color: #3396fb;
            }

            .mod-btn-white:focus {
                color: #fff;
                background-color: #0064ed;
            }

        .mod-btn-disabled-blue {
            color: #fff;
            background-color: #cce5fe;
            cursor: default;
        }

        .mod-btn-disabled-orange {
            color: #fff;
            background-color: #ffca69;
            cursor: default;
        }

        .mod-btn-disabled-gray {
            color: #666;
            background-color: #e6e6e6;
            cursor: default;
        }

        .mod-btn-transparent {
            color: #666;
            border-color: #ededed;
            background-color: transparent;
        }

            .mod-btn-transparent:hover {
                color: #007cfa;
                border-color: #007cfa;
                background-color: transparent;
            }

        .mod-btn-qq {
            color: #666;
            background-color: #fff;
            border-color: #ededed;
        }

            .mod-btn-qq:hover {
                color: #ff8a00;
                border-color: #ff8a00;
            }

            .mod-btn-qq .i-qq {
                width: 22px;
                height: 22px;
                margin-bottom: 4px;
                margin-right: 7px;
                vertical-align: middle;
                background-image: url(../images/icon.png);
                background-position: 0 -535px;
            }
    </style>

    <!-- 添加Product.aspx中的字体替换样式 -->
    <style fr-css-3fb305a8="false" id="HMxJVhcBpBem" media="screen" type="text/css">
        :root {
            --fr-font-basefont: system-ui,-apple-system,BlinkMacSystemFont,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji','Android Emoji','EmojiSymbols','emojione mozilla','twemoji mozilla','office365icons','iconfont','icomoon','FontAwesome','Font Awesome 5 Pro','Font Awesome 6 Pro','IcoFont','fontello','themify','Material Icons','Material Icons Extended','bootstrap-icons','Segoe Fluent Icons','Material-Design-Iconic-Font';
            --fr-font-fontscale: 1;
            --fr-font-family: 'Microsoft YaHei UI';
            --fr-font-shadow: 0 0 0.75px #7c7c7cdd;
            --fr-font-stroke: 0.015px currentcolor;
            --fr-no-stroke: 0px transparent;
        }

        @font-face {
            font-family: "Arial";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "FangSong";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Georgia";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "HanHei SC";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Helvetica";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Helvetica Neue";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "KaiTi";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Microsoft YaHei";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "MingLiU";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "NSimSun";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Noto Sans";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Open Sans";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PMingLiU";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangHK-Medium";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangHK-Regular";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangSC-Medium";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangSC-Regular";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "PingFangSC-Semibold";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Roboto";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "RobotoDraft";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "SF Pro SC";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Segoe UI";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "SimHei";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "SimSun";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Tahoma";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Ubuntu";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "Verdana";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\4EFF\5B8B";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\5B8B\4F53";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\5FAE\8EDF\6B63\9ED1\9AD4";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\5FAE\8F6F\96C5\9ED1";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\6977\4F53";
            src: local("MicrosoftYaHeiUI");
        }

        @font-face {
            font-family: "\9ED1\4F53";
            src: local("MicrosoftYaHeiUI");
        }

        :root#AOrggp :is(:not(i,head *):not([class*='glyph'],[class*='icon'],[class*='fa-'],[class*='vjs-'],[class*='mu-'])) {
            font-family: var(--fr-font-family),var(--fr-font-basefont);
            text-shadow: var(--fr-font-shadow);
            -webkit-text-stroke: var(--fr-font-stroke);
            font-feature-settings: unset;
            font-variant: unset;
            font-optical-sizing: auto;
            font-kerning: auto;
            -webkit-font-smoothing: antialiased !important;
            text-rendering: optimizeLegibility;
        }

        :root#AOrggp ::selection {
            color: #ffffff !important;
            background: #0084ff !important;
        }

        :root#AOrggp :is(progress,meter,datalist,samp,kbd,pre,pre *,code,code *) {
            -webkit-text-stroke: var(--fr-no-stroke) !important;
            text-shadow: none !important
        }

        .fr-fix-cc18d11d {
            -webkit-text-stroke: var(--fr-no-stroke) !important;
        }

        .panai-popup {
            font-size: 14px !important
        }

        .panai-setting-label {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 20px;
        }

        .panai-setting-checkbox {
            width: 16px;
            height: 16px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- 直接渲染Product页面内容，去掉Iframe -->
    <div class="product-page-wrapper" style="margin-top: 60px;">

        <div class="camscanner_banner index_card">
            <div class="bd">
                <ul style="position: relative; width: 1903px; height: 400px;">
                    <li style="background-image: url(&quot;<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/bg3.jpg&quot;); position: absolute; width: 1903px; left: 0px; top: 0px; display: list-item;">
                        <div class="warp">
                        </div>
                    </li>
                    <li style="background-image: url(&quot;<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/banner1.png&quot;); position: absolute; width: 1903px; left: 0px; top: 0px; display: none;">
                        <div class="warp">
                        </div>
                    </li>
                    <li style="background-image: url(&quot;<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/image/camscanner_banner.png&quot;); position: absolute; width: 1903px; left: 0px; top: 0px; display: none;">
                        <div class="warp">
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hd">
                <ul>
                    <li class="on">1</li>
                    <li class="">2</li>
                    <li class="">3</li>
                    <li class="">4</li>
                </ul>
            </div>
        </div>
        <script>jQuery(".camscanner_banner").slide({ titCell: ".hd ul", mainCell: ".bd ul", effect: "fold", autoPlay: true, autoPage: true, trigger: "click", delayTime: 1000, interTime: 5000 });</script>
        <!-- the banner end -->

        <header>
            <h1 class="product-title" style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">OCR助手智能文字识别一体化解决方案</h1>
        </header>

        <!-- 应用场景 -->
        <nav class="camscanner_menu" id="tip" aria-label="产品导航">
            <div class="warp">
                <ul class="fl" style="width: 650px;">
                    <li style="width: 25%; margin-right: 0;"><a class="a_anli" href="#scene-section">应用场景</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_gongneng" href="#feature-section">功能介绍</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_fangan" href="#review-section">用户评价</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_ccc" href="#media-section">媒体报道</a></li>
                </ul>
                <ul class="fr" style="height: 46px; margin-top: 8px;">
                    <li class="style" style="line-height: 40px; width: 100%;"><a href="<%=Account.Web.CommonRequest.GetDownLoadUrl(Request) %>" style="height: 40px; width: 100%;"><b>立即下载</b></a></li>
                </ul>
            </div>
        </div>

        <div class="main warp a_anli_content">
            <div class="sales_x">
                <div class="biaoti">
                    <p class="tit mtit">电脑扫描仪，随时记录，轻松分享</p>
                    <p class="info minfo">"电脑上最好的100个软件之一"</p>
                </div>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.001.png" alt="pic"></dd>
                    <dt>轻松处理各种场景</dt>
                    <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.002.png" alt="pic"></dd>
                    <dt>自动图像识别预处理</dt>
                    <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
                </dl>
                <dl class="d1 kkel">
                    <dd class="pic">
                        <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.003.png" alt="pic"></dd>
                    <dt>打字到手酸的同学们的神器</dt>
                    <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
                </dl>
            </div>
        </div>
        <!-- 产品优势 end -->
        <div class="clear"></div>
        <main>
        <section id="feature-section" class="advantage a_gongneng_content">
            <div class="tu_a">
                <div class="warp">
                    <div class="ccb_tr">
                        <h2 class="ccb_rt_a">AI-智能OCR识别</h2>
                        <div class="ccb_rt_b">
                            <p>截图/拍照/文件，一键操作，深度加速<br>
                                支持识别100+语言，助力全球业务展开<br>
                                账号特权，云端存储，不限设备，不限次数。<br>
                                出差/异地，随时登录，随时使用！
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tu_e">
                <div class="warp">
                    <div class="ccb_tr linx">
                        <h2 class="ccb_rt_a">
                            轻松处理各种文档
                        </h2>
                        <div class="ccb_rt_b">
                            <p>支持Office全家桶(Word,PPT,Excel等)<br>
                                支持PDF文档扫描及转换<br>
                                支持全文扫描，支持全文翻译<br>
                                各种办公文档智能解析，处理结果支持一键下载
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tu_b">
                <div class="warp">
                    <div class="ccb_lr">
                        <div class="ccb_rt_a">云上OCR，轻享服务</div>
                        <div class="ccb_rt_b">
                            <span>依托于助手高性能服务器，用户只需要一个账号<br>
                                即可轻松享用各种大厂提供的服务<br>
                                众星捧月，只为让您提升工作生活效率！<br>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <section id="review-section" class="advantage a_fangan_content">
            <div class="ying camying">
                <!-- 用户评价 -->
                <div class="user">
                    <h2 class="titx">用户评价</h2>
                    <div class="user_pj">
                        <div class="user_pj_a">
                            <div class="bd bd_x">
                                <div class="tempWrap" style="overflow: hidden; position: relative; width: 1021px">
                                    <ul style="width: 4084px; left: -1992.58px; position: relative; overflow: hidden; padding: 0px; margin: 0px;">
                                        <li style="float: left; width: 1021px;"><span>工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！</span></li>
                                        <li style="float: left; width: 1021px;"><span>审计人员尤其适合使用，图片处理之后效果灰常好，很清晰，值得推荐。就查账翻凭证的时候啊，就你懂的啊，就有了OCR助手超级方便呢～</span></li>
                                        <li style="float: left; width: 1021px;"><span>门诊每天的手写病历和处方可以用OCR助手，很容易的将图片转成数字化，并存储，在没有实现数字化系统的小门诊可以很容易的实现病例和处方的讨论！有不足但是很棒的软件！</span></li>
                                        <li style="float: left; width: 1021px;"><span>不得不说，对于学生党太好用了，书上的重点都可以OCR识别下来再排版打印，写论文到图书馆拍资料都是用的它，很喜欢。推荐给好多同学用~</span></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="hd bd_xe">
                                <ul>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.005.png" alt="办公族"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">John</span>
                                            <span class="hd_bx_b">办公族</span>
                                        </span>
                                    </li>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.006.png" alt="审计"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Abby</span>
                                            <span class="hd_bx_b">审计</span>
                                        </span>
                                    </li>
                                    <li class="on">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.007.png" alt="医生"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Han</span>
                                            <span class="hd_bx_b">医生</span>
                                        </span>
                                    </li>
                                    <li class="">
                                        <span class="hd_ax">
                                            <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/cam.008.png" alt="学生"></span>
                                        <span class="hd_bx">
                                            <span class="hd_bx_a">Mia</span>
                                            <span class="hd_bx_b">学生</span>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <script type="text/javascript">jQuery(".user_pj_a").slide({ mainCell: ".bd ul", effect: "left", autoPlay: true });</script>

                        </div>
                    </div>
                </div>
                <!-- 用户评价 end -->
            </div>
        </div>

        <section id="media-section" class="media warp camsmedia a_ccc_content" style="margin-top: 80px;">
            <h2 class="tit">媒体报道</h2>
            <div class="news_bd">
                <div class="slideBoxx">
                    <div class="bd">
                        <ul>
                            <li style="display: none;">
                                <span class="news_bd_a">
                                    <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c103.png"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=*************">电脑上最好的50个软件之一</a></span>
                            </li>
                            <li style="display: none;">
                                <span class="news_bd_a">
                                    <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c102.png"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=*************">OCR助手，超级扫描仪</a></span>
                            </li>
                            <li style="display: list-item;">
                                <span class="news_bd_a">
                                    <img src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/picture/c100.png"></span>
                                <span class="news_bd_b"><a href="javascript:;?t=*************">上班族必备10款应用，OCR助手榜上有名</a></span>
                            </li>
                        </ul>
                    </div>
                    <div class="h36"></div>
                    <div class="hd">
                        <ul>
                            <li class=""></li>
                            <li class=""></li>
                            <li class="on"></li>
                        </ul>
                    </div>
                    <!-- 下面是前/后按钮代码，如果不需要删除即可 -->
                </div>
                <script type="text/javascript">
                    jQuery(".slideBoxx").slide({
                        mainCell: ".bd ul",
                        autoPlay: true
                    });
                </script>
            </div>
        </div>
        </main>
        <br />
        <br />
        <br />
    </div>

    <!-- 引入Product页面的JS文件 -->
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/wp-embed.min.js"></script>
    <script type="text/javascript" src="<%=Account.Web.CommonRequest.GetStaticUrl(Request) %>static/v1/js/common_new.js"></script>
</asp:Content>
