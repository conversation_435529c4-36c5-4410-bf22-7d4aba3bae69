﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace Account.Web
{
    class MD5Helper
    {

        public static string ToMD5(string str)
        {
            MD5CryptoServiceProvider hashmd5;
            hashmd5 = new MD5CryptoServiceProvider();
            return BitConverter.ToString(hashmd5.ComputeHash(Encoding.Default.GetBytes(str))).Replace("-", "").ToLower();
        }
    }
}
