<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试Detail页面</title>
    <style>
        /* 基本样式测试 */
        .product-page-wrapper {
            width: 100%;
            min-height: 100vh;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .camscanner_banner {
            height: 400px;
            background: #f5f5f5;
            position: relative;
            overflow: hidden;
        }
        
        .camscanner_banner .bd ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .camscanner_banner .bd li {
            background-size: cover;
            background-position: center;
        }
        
        .camscanner_menu {
            background: #fff;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .warp {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        .fl {
            float: left;
        }
        
        .fr {
            float: right;
        }
        
        .clear {
            clear: both;
        }
        
        .camscanner_menu ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .camscanner_menu li {
            display: inline-block;
        }
        
        .camscanner_menu a {
            text-decoration: none;
            color: #333;
            padding: 10px 15px;
            display: block;
            transition: color 0.3s;
        }
        
        .camscanner_menu a:hover {
            color: #007cfa;
        }
        
        .main {
            padding: 60px 0;
            background: #fff;
        }
        
        .biaoti {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .biaoti .tit {
            font-size: 32px;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .biaoti .info {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }
        
        .sales_x {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .sales_x .d1 {
            flex: 1;
            min-width: 300px;
            text-align: center;
            padding: 30px 20px;
            background: #f9f9f9;
            border-radius: 8px;
            transition: transform 0.3s;
        }
        
        .sales_x .d1:hover {
            transform: translateY(-5px);
        }
        
        .sales_x .pic img {
            max-width: 100%;
            height: auto;
            margin-bottom: 20px;
        }
        
        .sales_x dt {
            font-size: 20px;
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .sales_x .info {
            color: #666;
            line-height: 1.6;
        }
        
        .advantage {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .tu_a, .tu_b, .tu_e {
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .ccb_tr {
            display: flex;
            align-items: center;
            gap: 50px;
        }
        
        .ccb_rt_a {
            font-size: 28px;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .ccb_rt_b p {
            font-size: 16px;
            color: #666;
            line-height: 1.8;
        }
        
        .user {
            padding: 80px 0;
            background: #fff;
        }
        
        .titx {
            text-align: center;
            font-size: 32px;
            color: #333;
            margin-bottom: 50px;
            font-weight: bold;
        }
        
        .media {
            padding: 80px 0;
            background: #f8f9fa;
        }
        
        .tit {
            text-align: center;
            font-size: 32px;
            color: #333;
            margin-bottom: 50px;
            font-weight: bold;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sales_x {
                flex-direction: column;
            }
            
            .ccb_tr {
                flex-direction: column;
                text-align: center;
            }
            
            .camscanner_menu .fl {
                width: 100% !important;
            }
            
            .camscanner_menu .fr {
                width: 100%;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="product-page-wrapper">
        <div class="camscanner_banner index_card">
            <div class="bd">
                <ul style="position: relative; width: 100%; height: 400px;">
                    <li style="background-image: url('https://via.placeholder.com/1200x400/007cfa/ffffff?text=OCR助手'); position: absolute; width: 100%; left: 0px; top: 0px; display: list-item;">
                        <div class="warp">
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <header>
            <h1 class="product-title" style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">OCR助手智能文字识别一体化解决方案</h1>
        </header>
        
        <nav class="camscanner_menu" id="tip" aria-label="产品导航">
            <div class="warp">
                <ul class="fl" style="width: 650px;">
                    <li style="width: 25%; margin-right: 0;"><a class="a_anli" href="#scene-section">应用场景</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_gongneng" href="#feature-section">功能介绍</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_fangan" href="#review-section">用户评价</a></li>
                    <li style="width: 25%; margin-right: 0;"><a class="a_ccc" href="#media-section">媒体报道</a></li>
                </ul>
                <ul class="fr" style="height: 46px; margin-top: 8px;">
                    <li class="style" style="line-height: 40px; width: 100%;"><a href="#" style="height: 40px; width: 100%; background: #007cfa; color: white; text-decoration: none; display: block; text-align: center; border-radius: 4px;"><b>立即下载</b></a></li>
                </ul>
                <div class="clear"></div>
            </div>
        </nav>

        <div class="main warp a_anli_content">
            <div class="sales_x">
                <div class="biaoti" style="width: 100%; margin-bottom: 50px;">
                    <p class="tit mtit">电脑扫描仪，随时记录，轻松分享</p>
                    <p class="info minfo">"电脑上最好的100个软件之一"</p>
                </div>
                <dl class="d1">
                    <dd class="pic">
                        <img src="https://via.placeholder.com/200x150/f0f0f0/333333?text=场景1" alt="pic"></dd>
                    <dt>轻松处理各种场景</dt>
                    <span class="info">办公一族非常需要，能非常方便的处理转换图文</span>
                </dl>
                <dl class="d1">
                    <dd class="pic">
                        <img src="https://via.placeholder.com/200x150/f0f0f0/333333?text=预处理" alt="pic"></dd>
                    <dt>自动图像识别预处理</dt>
                    <span class="info">图片扫描生成自动锐化提亮，倾斜矫正</span>
                </dl>
                <dl class="d1 kkel">
                    <dd class="pic">
                        <img src="https://via.placeholder.com/200x150/f0f0f0/333333?text=OCR" alt="pic"></dd>
                    <dt>打字到手酸的同学们的神器</dt>
                    <span class="info">OCR识别，图片瞬间变文本，告别打字打到手酸</span>
                </dl>
            </div>
        </div>
        
        <div class="clear"></div>
        <main>
        <section id="feature-section" class="advantage a_gongneng_content">
            <div class="tu_a">
                <div class="warp">
                    <div class="ccb_tr">
                        <h2 class="ccb_rt_a">AI-智能OCR识别</h2>
                        <div class="ccb_rt_b">
                            <p>截图/拍照/文件，一键操作，深度加速<br>
                                支持识别100+语言，助力全球业务展开<br>
                                账号特权，云端存储，不限设备，不限次数。<br>
                                出差/异地，随时登录，随时使用！
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="review-section" class="advantage a_fangan_content">
            <div class="ying camying">
                <div class="user">
                    <h2 class="titx">用户评价</h2>
                    <div class="user_pj">
                        <p style="text-align: center; color: #666; font-size: 16px;">工作原因要经常扫描东西发邮件给客户，用的机会多。OCR助手自动识别，剪裁方便，关键能有多种格式互相转换，简直逆天哦哦哦，真心推荐！！</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="media-section" class="media warp camsmedia a_ccc_content" style="margin-top: 80px;">
            <h2 class="tit">媒体报道</h2>
            <div class="news_bd">
                <p style="text-align: center; color: #666; font-size: 16px;">上班族必备10款应用，OCR助手榜上有名</p>
            </div>
        </section>
        </main>
    </div>
</body>
</html>
