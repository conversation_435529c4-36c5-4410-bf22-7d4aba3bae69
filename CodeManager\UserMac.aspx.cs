﻿using CommonLib;
using System;

namespace Account.Web
{
    public partial class UserMac : System.Web.UI.Page
    {
        public string strErrorMsg = string.Empty;

        protected void Page_Load(object sender, EventArgs e)
        {
            var session = Account.Web.UserLoginInfoHelper.GetUserInfo(Request, Response);
            string strOp = Request.QueryString["op"];
            string uid = Request.QueryString["id"];
            if (Request.RequestType.Equals("GET") && !string.IsNullOrEmpty(strOp) && BoxUtil.IsAlphaNumeric(uid))
            {
                var result = true;
                var lang = Request.GetValue("lang");
                try
                {
                    switch (strOp)
                    {
                        case "offline":
                            OffLineByToken(session.Account, uid);
                            break;
                        case "enable":
                            result = CodeHelper.UpdateUserDataState(session.Account, uid, true);
                            break;
                        case "disable":
                            result = CodeHelper.UpdateUserDataState(session.Account, uid, false);
                            OffLineByToken(session.Account, uid);
                            break;
                    }
                }
                catch { }
                Response.Write("<script>alert('" + ("操作" + (result ? "成功" : "失败") + "！").GetTrans(lang) + "');window.location='UserMac.aspx'</script>");
            }
        }

        private void OffLineByToken(string account, string uid)
        {
            var token = CodeHelper.GetUserToken(account, uid);
            if (!string.IsNullOrEmpty(token))
                RdsCacheHelper.LstAccountCache.ClearToken(account, token);
        }
    }
}